{"name": "nutri-ai-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~53.0.17", "expo-blur": "~14.0.1", "expo-camera": "~16.0.8", "expo-image-picker": "~16.0.3", "expo-linear-gradient": "~14.0.1", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "invariant": "^2.2.4", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.22.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.8.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}