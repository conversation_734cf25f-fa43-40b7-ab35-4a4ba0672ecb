export const Colors = {
  // Apple-Inspired Shadcn Design System
  background: '#ffffff',
  backgroundSecondary: '#fafafa',
  backgroundTertiary: '#f4f4f5',
  foreground: '#09090b',

  // Sophisticated Card System
  card: '#ffffff',
  cardForeground: '#09090b',
  cardBorder: '#e4e4e7',
  cardShadow: 'rgba(0, 0, 0, 0.05)',

  // Modern Popover System
  popover: '#ffffff',
  popoverForeground: '#09090b',

  // Clean Primary System
  primary: '#18181b',
  primaryForeground: '#fafafa',

  // Subtle Secondary System
  secondary: '#f4f4f5',
  secondaryForeground: '#71717a',

  // Refined Muted System
  muted: '#f4f4f5',
  mutedForeground: '#71717a',

  // Modern Accent System
  accent: '#f4f4f5',
  accentForeground: '#18181b',

  // Clean Destructive System
  destructive: '#ef4444',
  destructiveForeground: '#fafafa',

  // Minimal Border System
  border: '#e4e4e7',
  input: '#e4e4e7',
  ring: '#18181b',

  // Sophisticated Dark Green & Olive Brand System
  brand: '#2d5016',           // Rich dark green
  brandSecondary: '#4a6741',  // Sophisticated olive green
  brandTertiary: '#1a3009',   // Deep forest green
  brandForeground: '#ffffff',
  brandMuted: '#f0f4ed',      // Ultra light green
  brandSubtle: '#e6ede0',     // Soft green tint
  brandLight: '#c8d4bf',      // Light green
  brandDark: '#1a3009',       // Very dark green
  brandAccent: '#5a7c4d',     // Medium olive

  // Brand Gradients
  brandGradient: 'linear-gradient(135deg, #2d5016 0%, #4a6741 100%)',
  brandGradientReverse: 'linear-gradient(135deg, #4a6741 0%, #2d5016 100%)',
  brandGradientVertical: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',
  brandGradientSubtle: 'linear-gradient(135deg, #f0f4ed 0%, #e6ede0 100%)',

  // Brand Effects
  brandGlow: 'rgba(45, 80, 22, 0.2)',
  brandShadow: 'rgba(45, 80, 22, 0.1)',

  // Enhanced background system
  backgroundPrimary: '#ffffff',
  backgroundSecondary: '#fafbfa',
  backgroundTertiary: '#f5f7f5',
  backgroundAccent: '#f0f9f0',

  // Enhanced semantic colors with light variants
  success: '#10b981',
  successLight: '#ecfdf5',
  successDark: '#047857',
  warning: '#f59e0b',
  warningLight: '#fffbeb',
  warningDark: '#d97706',
  error: '#ef4444',
  errorLight: '#fef2f2',
  errorDark: '#dc2626',
  info: '#3b82f6',
  infoLight: '#eff6ff',
  infoDark: '#2563eb',

  // Advanced glassmorphism effects
  glass: 'rgba(255, 255, 255, 0.85)',
  glassBorder: 'rgba(255, 255, 255, 0.2)',
  glassStrong: 'rgba(255, 255, 255, 0.95)',
  glassDark: 'rgba(0, 0, 0, 0.1)',
  glassGreen: 'rgba(45, 80, 22, 0.1)',

  // Professional shadow system
  shadowXs: 'rgba(0, 0, 0, 0.05)',
  shadowSm: 'rgba(0, 0, 0, 0.08)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowMd: 'rgba(0, 0, 0, 0.15)',
  shadowLg: 'rgba(0, 0, 0, 0.2)',
  shadowXl: 'rgba(0, 0, 0, 0.25)',
  shadowBrand: 'rgba(45, 80, 22, 0.15)',
  shadowBrandLight: 'rgba(45, 80, 22, 0.08)',

  // Gradient overlays
  gradientOverlay: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.4) 100%)',
  gradientOverlayLight: 'linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 100%)',
  gradientShimmer: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%)',
};

export const Spacing = {
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  xxxl: 24,
  xxxxl: 32,
};

export const BorderRadius = {
  none: 0,
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  full: 9999,
};

export const Shadows = {
  xs: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.2,
    shadowRadius: 24,
    elevation: 12,
  },
  brand: {
    shadowColor: '#16a34a',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
};

export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
};

export const FontWeights = {
  light: '300' as const,
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  black: '900' as const,
};
