import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, SlideInLeft } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import {
  usePressAnimation,
  useBounceAnimation,
  useFadeAnimation,
  useSlideAnimation,
  useScaleAnimation,
  useRotationAnimation,
  useShakeAnimation,
  usePulseAnimation,
  useStaggeredListAnimation,
} from '../hooks/useAnimations';
import {
  SwipeableCard,
  PressableScale,
  Draggable,
  PinchZoom,
} from '../components/GestureComponents';
import {
  FadeTransition,
  SlideTransition,
  ScaleTransition,
  StaggeredTransition,
} from '../components/TransitionComponents';

const AnimationShowcaseScreen: React.FC = () => {
  const [showBounce, setShowBounce] = useState(false);
  const [showFade, setShowFade] = useState(true);
  const [showSlide, setShowSlide] = useState(true);
  const [showScale, setShowScale] = useState(true);
  const [showRotation, setShowRotation] = useState(false);
  const [showShake, setShowShake] = useState(false);
  const [showPulse, setShowPulse] = useState(false);
  const [showStaggered, setShowStaggered] = useState(true);

  // Animation hooks
  const { animatedStyle: pressStyle, handlePressIn, handlePressOut } = usePressAnimation();
  const { animatedStyle: bounceStyle } = useBounceAnimation(showBounce);
  const { animatedStyle: fadeStyle } = useFadeAnimation(showFade);
  const { animatedStyle: slideStyle } = useSlideAnimation(showSlide, 'left');
  const { animatedStyle: scaleStyle } = useScaleAnimation(showScale);
  const { animatedStyle: rotationStyle } = useRotationAnimation(showRotation);
  const { animatedStyle: shakeStyle } = useShakeAnimation(showShake);
  const { animatedStyle: pulseStyle } = usePulseAnimation(showPulse);
  const { getItemStyle } = useStaggeredListAnimation(4, showStaggered);

  const demoItems = [
    { id: 1, title: 'Item 1', icon: 'star' as const },
    { id: 2, title: 'Item 2', icon: 'heart' as const },
    { id: 3, title: 'Item 3', icon: 'flash' as const },
    { id: 4, title: 'Item 4', icon: 'leaf' as const },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <Animated.View entering={FadeInUp.duration(600)} style={styles.header}>
        <Text style={styles.title}>Animation Showcase</Text>
        <Text style={styles.subtitle}>Advanced animations and transitions</Text>
      </Animated.View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Basic Animations Section */}
        <Animated.View entering={SlideInLeft.delay(200).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Animations</Text>
          
          <View style={styles.animationGrid}>
            {/* Press Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Press Animation</Text>
              <Animated.View style={[styles.demoBox, pressStyle]}>
                <TouchableOpacity
                  style={styles.demoButton}
                  onPressIn={handlePressIn}
                  onPressOut={handlePressOut}
                  activeOpacity={1}
                >
                  <Ionicons name="touch" size={24} color={Colors.brand} />
                </TouchableOpacity>
              </Animated.View>
            </ModernCard>

            {/* Bounce Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Bounce</Text>
              <Animated.View style={[styles.demoBox, bounceStyle]}>
                <Ionicons name="basketball" size={24} color={Colors.warning} />
              </Animated.View>
              <ModernButton
                title="Bounce"
                onPress={() => setShowBounce(!showBounce)}
                variant="outline"
                size="sm"
              />
            </ModernCard>

            {/* Fade Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Fade</Text>
              <Animated.View style={[styles.demoBox, fadeStyle]}>
                <Ionicons name="eye" size={24} color={Colors.info} />
              </Animated.View>
              <ModernButton
                title="Toggle"
                onPress={() => setShowFade(!showFade)}
                variant="outline"
                size="sm"
              />
            </ModernCard>

            {/* Scale Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Scale</Text>
              <Animated.View style={[styles.demoBox, scaleStyle]}>
                <Ionicons name="resize" size={24} color={Colors.success} />
              </Animated.View>
              <ModernButton
                title="Scale"
                onPress={() => setShowScale(!showScale)}
                variant="outline"
                size="sm"
              />
            </ModernCard>
          </View>
        </Animated.View>

        {/* Advanced Animations Section */}
        <Animated.View entering={SlideInLeft.delay(400).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Advanced Animations</Text>
          
          <View style={styles.animationGrid}>
            {/* Rotation Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Rotation</Text>
              <Animated.View style={[styles.demoBox, rotationStyle]}>
                <Ionicons name="refresh" size={24} color={Colors.brand} />
              </Animated.View>
              <ModernButton
                title="Rotate"
                onPress={() => setShowRotation(!showRotation)}
                variant="outline"
                size="sm"
              />
            </ModernCard>

            {/* Shake Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Shake</Text>
              <Animated.View style={[styles.demoBox, shakeStyle]}>
                <Ionicons name="warning" size={24} color={Colors.error} />
              </Animated.View>
              <ModernButton
                title="Shake"
                onPress={() => setShowShake(!showShake)}
                variant="outline"
                size="sm"
              />
            </ModernCard>

            {/* Pulse Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Pulse</Text>
              <Animated.View style={[styles.demoBox, pulseStyle]}>
                <Ionicons name="heart" size={24} color={Colors.error} />
              </Animated.View>
              <ModernButton
                title="Pulse"
                onPress={() => setShowPulse(!showPulse)}
                variant="outline"
                size="sm"
              />
            </ModernCard>

            {/* Slide Animation */}
            <ModernCard variant="default" style={styles.animationCard}>
              <Text style={styles.cardTitle}>Slide</Text>
              <Animated.View style={[styles.demoBox, slideStyle]}>
                <Ionicons name="arrow-forward" size={24} color={Colors.info} />
              </Animated.View>
              <ModernButton
                title="Slide"
                onPress={() => setShowSlide(!showSlide)}
                variant="outline"
                size="sm"
              />
            </ModernCard>
          </View>
        </Animated.View>

        {/* Gesture Animations Section */}
        <Animated.View entering={SlideInLeft.delay(600).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Gesture Animations</Text>
          
          {/* Swipeable Card */}
          <ModernCard variant="default" style={styles.gestureCard}>
            <Text style={styles.cardTitle}>Swipeable Card</Text>
            <Text style={styles.cardSubtitle}>Swipe left or right</Text>
            <SwipeableCard
              onSwipeLeft={() => console.log('Swiped left')}
              onSwipeRight={() => console.log('Swiped right')}
              style={styles.swipeableDemo}
            >
              <View style={styles.swipeableContent}>
                <Ionicons name="swap-horizontal" size={32} color={Colors.brand} />
                <Text style={styles.swipeableText}>Swipe me!</Text>
              </View>
            </SwipeableCard>
          </ModernCard>

          {/* Pressable Scale */}
          <ModernCard variant="default" style={styles.gestureCard}>
            <Text style={styles.cardTitle}>Pressable Scale</Text>
            <Text style={styles.cardSubtitle}>Press and hold for long press</Text>
            <PressableScale
              onPress={() => console.log('Pressed')}
              onLongPress={() => console.log('Long pressed')}
              style={styles.pressableDemo}
            >
              <View style={styles.pressableContent}>
                <Ionicons name="hand-left" size={32} color={Colors.success} />
                <Text style={styles.pressableText}>Press me!</Text>
              </View>
            </PressableScale>
          </ModernCard>

          {/* Draggable */}
          <ModernCard variant="default" style={styles.gestureCard}>
            <Text style={styles.cardTitle}>Draggable</Text>
            <Text style={styles.cardSubtitle}>Drag around the screen</Text>
            <View style={styles.draggableContainer}>
              <Draggable snapBack style={styles.draggableDemo}>
                <View style={styles.draggableContent}>
                  <Ionicons name="move" size={24} color={Colors.warning} />
                </View>
              </Draggable>
            </View>
          </ModernCard>
        </Animated.View>

        {/* Staggered Animation Section */}
        <Animated.View entering={SlideInLeft.delay(800).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Staggered Animations</Text>
          
          <ModernCard variant="default" style={styles.staggeredCard}>
            <View style={styles.staggeredHeader}>
              <Text style={styles.cardTitle}>List Animation</Text>
              <ModernButton
                title="Toggle"
                onPress={() => setShowStaggered(!showStaggered)}
                variant="outline"
                size="sm"
              />
            </View>
            
            <View style={styles.staggeredList}>
              {demoItems.map((item, index) => (
                <Animated.View key={item.id} style={[styles.staggeredItem, getItemStyle(index)]}>
                  <View style={styles.staggeredItemContent}>
                    <Ionicons name={item.icon} size={20} color={Colors.brand} />
                    <Text style={styles.staggeredItemText}>{item.title}</Text>
                  </View>
                </Animated.View>
              ))}
            </View>
          </ModernCard>
        </Animated.View>

        {/* Transition Components Section */}
        <Animated.View entering={SlideInLeft.delay(1000).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Transition Components</Text>
          
          <ModernCard variant="default" style={styles.transitionCard}>
            <Text style={styles.cardTitle}>Transition Examples</Text>
            
            <View style={styles.transitionGrid}>
              <FadeTransition visible={showFade} style={styles.transitionBox}>
                <Text style={styles.transitionText}>Fade</Text>
              </FadeTransition>
              
              <SlideTransition visible={showSlide} direction="up" style={styles.transitionBox}>
                <Text style={styles.transitionText}>Slide</Text>
              </SlideTransition>
              
              <ScaleTransition visible={showScale} style={styles.transitionBox}>
                <Text style={styles.transitionText}>Scale</Text>
              </ScaleTransition>
            </View>
          </ModernCard>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xxl,
  },
  sectionTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  animationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    justifyContent: 'space-between',
  },
  animationCard: {
    width: '48%',
    padding: Spacing.lg,
    alignItems: 'center',
    gap: Spacing.md,
  },
  cardTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    textAlign: 'center',
  },
  cardSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  demoBox: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  demoButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gestureCard: {
    padding: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  swipeableDemo: {
    marginTop: Spacing.md,
  },
  swipeableContent: {
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    alignItems: 'center',
    gap: Spacing.md,
  },
  swipeableText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  pressableDemo: {
    marginTop: Spacing.md,
  },
  pressableContent: {
    backgroundColor: Colors.successMuted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    alignItems: 'center',
    gap: Spacing.md,
  },
  pressableText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.success,
  },
  draggableContainer: {
    height: 120,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    marginTop: Spacing.md,
    position: 'relative',
  },
  draggableDemo: {
    position: 'absolute',
    top: 20,
    left: 20,
  },
  draggableContent: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.warning,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },
  staggeredCard: {
    padding: Spacing.xl,
  },
  staggeredHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  staggeredList: {
    gap: Spacing.md,
  },
  staggeredItem: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
  },
  staggeredItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  staggeredItemText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  transitionCard: {
    padding: Spacing.xl,
  },
  transitionGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  transitionBox: {
    flex: 1,
    height: 80,
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  transitionText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.brandForeground,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default AnimationShowcaseScreen;
