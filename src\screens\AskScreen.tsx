import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import AnimatedText from '../components/AnimatedText';
import ApiService from '../services/ApiService';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const AskScreen: React.FC = () => {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleAsk = async () => {
    if (!question.trim()) return;

    setLoading(true);
    setAnswer(null);

    try {
      const result = await ApiService.askQuestion(question);
      setAnswer(result.answer);
    } catch (error) {
      Alert.alert('Error', 'Failed to get answer. Please try again.');
      console.error('Q&A error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.backgroundSecondary]}
        style={styles.backgroundGradient}
      >
        <KeyboardAvoidingView 
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
            <Text style={styles.title}>Ask NutriAI</Text>
            <Text style={styles.subtitle}>Your personal nutrition expert.</Text>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              value={question}
              onChangeText={setQuestion}
              placeholder="e.g., 'Are high-protein diets safe for kidneys?'"
              placeholderTextColor={Colors.textLight}
              multiline
              numberOfLines={4}
            />

            <TouchableOpacity
              style={[styles.askButton, loading && styles.askButtonDisabled]}
              onPress={handleAsk}
              disabled={loading}
            >
              <LinearGradient
                colors={loading ? [Colors.gray400, Colors.gray500] : [Colors.primary, Colors.secondary]}
                style={styles.buttonGradient}
              >
                <Ionicons
                  name={loading ? "hourglass" : "sparkles"}
                  size={24}
                  color={Colors.white}
                />
                <Text style={styles.buttonText}>
                  {loading ? 'Getting Answer...' : 'Get Answer'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Answer Display */}
          {answer && (
            <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.answerContainer}>
              <View style={styles.answerCard}>
                <AnimatedText
                  text={answer}
                  style={styles.answerText}
                />
              </View>
            </Animated.View>
          )}
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundGradient: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    textAlign: 'center',
  },
  inputContainer: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  textInput: {
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: FontSizes.base,
    color: Colors.text,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: Spacing.md,
  },
  askButton: {
    width: '100%',
  },
  askButtonDisabled: {
    opacity: 0.7,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  buttonText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
  answerContainer: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xxl,
  },
  answerCard: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  answerText: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    lineHeight: 24,
  },
});

export default AskScreen;
