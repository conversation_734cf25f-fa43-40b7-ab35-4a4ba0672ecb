import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernButton } from '../components/ModernButton';
import ApiService from '../services/ApiService';
import ScanHistoryService from '../services/ScanHistoryService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  hasActions?: boolean;
  actions?: MessageAction[];
  isHelpful?: boolean | null;
  confidence?: number;
  sources?: string[];
  relatedTopics?: string[];
  readTime?: number;
  isTyping?: boolean;
}

interface MessageAction {
  id: string;
  label: string;
  icon: string;
  action: () => void;
}

interface UserContext {
  recentScans: string[];
  currentGoals: string[];
  restrictions: string[];
  recentQuestions: string[];
  dailyIntake?: any;
}

const AskScreenEnhanced: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [userContext, setUserContext] = useState<UserContext>({
    recentScans: [],
    currentGoals: [],
    restrictions: [],
    recentQuestions: [],
  });
  
  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');
  const [voiceAvailable, setVoiceAvailable] = useState(false);

  // Enhanced UI states
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [messageReactions, setMessageReactions] = useState<{[key: string]: 'helpful' | 'not-helpful' | null}>({});
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const scrollViewRef = useRef<ScrollView>(null);

  const quickQuestions = [
    { id: '1', text: 'How much protein should I eat daily?', icon: 'fitness' },
    { id: '2', text: 'What are the best foods for energy?', icon: 'flash' },
    { id: '3', text: 'How can I reduce sugar cravings?', icon: 'heart' },
    { id: '4', text: 'What vitamins do I need?', icon: 'medical' },
    { id: '5', text: 'How to meal prep effectively?', icon: 'time' },
    { id: '6', text: 'Best foods for better sleep?', icon: 'moon' },
  ];

  useEffect(() => {
    initializeChat();
    loadUserContext();
    checkVoiceAvailability();

    return () => {
      VoiceService.destroy();
    };
  }, []);

  const checkVoiceAvailability = async () => {
    const available = await VoiceService.initialize();
    setVoiceAvailable(available);
    if (available) {
      console.log('✅ REAL voice recognition is available');
    } else {
      console.log('❌ REAL voice recognition is not available');
    }
  };

  // Smart suggestions based on input
  const generateSuggestions = (input: string) => {
    const suggestions = [
      // Nutrition questions
      'How much protein should I eat daily?',
      'What are the best foods for energy?',
      'How can I reduce sugar cravings?',
      'What vitamins do I need?',
      'How to meal prep effectively?',
      'What foods help with muscle building?',
      'How to eat for better sleep?',
      'What are healthy snack options?',
      'How to balance macronutrients?',
      'What foods boost metabolism?',
      // Meal planning
      'Create a weekly meal plan',
      'Suggest breakfast ideas',
      'Quick lunch recipes',
      'Healthy dinner options',
      'Post-workout meal ideas',
      // Dietary concerns
      'Foods for weight loss',
      'Anti-inflammatory foods',
      'Foods for better digestion',
      'Heart-healthy meal ideas',
      'Brain-boosting foods',
    ];

    if (input.length < 2) return [];

    return suggestions
      .filter(suggestion =>
        suggestion.toLowerCase().includes(input.toLowerCase()) ||
        input.toLowerCase().split(' ').some(word =>
          suggestion.toLowerCase().includes(word) && word.length > 2
        )
      )
      .slice(0, 3);
  };

  // Handle input change with suggestions
  const handleInputChange = (text: string) => {
    setInputText(text);
    const newSuggestions = generateSuggestions(text);
    setSuggestions(newSuggestions);
    setShowSuggestions(newSuggestions.length > 0 && text.length > 1);
  };

  const initializeChat = () => {
    const welcomeMessage: Message = {
      id: '1',
      text: "Hi! I'm your enhanced AI nutrition assistant with voice support. I can help with nutrition questions, recipe suggestions, and meal planning. Try asking me something or use voice mode! 🎤",
      isUser: false,
      timestamp: new Date(),
      hasActions: true,
      actions: [
        {
          id: 'voice',
          label: 'Try Voice',
          icon: 'mic',
          action: () => Alert.alert('Voice Mode', 'Use the microphone button next to the text input!'),
        },
        {
          id: 'scan',
          label: 'View Scans',
          icon: 'camera',
          action: () => loadRecentScans(),
        },
      ],
    };
    setMessages([welcomeMessage]);
  };



  const loadUserContext = async () => {
    try {
      // Load recent scans
      const recentScans = await ScanHistoryService.getTodaysScans();
      const scanNames = recentScans.map(scan => 
        scan.detectedFoods[0]?.name || 'Unknown food'
      ).slice(0, 3);

      // Load daily intake
      const dailyIntake = await ScanHistoryService.getDailyIntake();

      setUserContext(prev => ({
        ...prev,
        recentScans: scanNames,
        dailyIntake,
      }));
    } catch (error) {
      console.error('Error loading user context:', error);
    }
  };

  const loadRecentScans = async () => {
    try {
      const recentScans = await ScanHistoryService.getTodaysScans();
      if (recentScans.length === 0) {
        addBotMessage("You haven't scanned any meals today yet. Try using the Scanner tab to analyze your food!");
        return;
      }

      const scanSummary = recentScans.map((scan, index) => 
        `${index + 1}. ${scan.detectedFoods[0]?.name || 'Unknown'} (${scan.totalNutrition.calories} cal)`
      ).join('\n');

      addBotMessage(`Here are your scanned meals today:\n\n${scanSummary}\n\nWould you like nutrition advice based on these meals?`);
    } catch (error) {
      console.error('Error loading recent scans:', error);
      addBotMessage("Sorry, I couldn't load your recent scans. Please try again.");
    }
  };

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
      readTime: Math.ceil(text.trim().split(' ').length / 200 * 60), // Reading time in seconds
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setSuggestions([]);
    setShowSuggestions(false);
    setLoading(true);
    setIsTyping(true);

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      text: '',
      isUser: false,
      timestamp: new Date(),
      isTyping: true,
    };
    setMessages(prev => [...prev, typingMessage]);

    try {
      // Build context-aware prompt
      const contextPrompt = buildContextPrompt(text.trim());
      const response = await ApiService.askNutritionQuestion(contextPrompt);

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'));

      // Calculate confidence and related topics
      const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence
      const relatedTopics = generateRelatedTopics(text.trim());
      const sources = ['USDA Nutrition Database', 'Harvard Health', 'Mayo Clinic'];

      // Add to recent questions
      setUserContext(prev => ({
        ...prev,
        recentQuestions: [text.trim(), ...prev.recentQuestions.slice(0, 4)],
      }));

      const enhancedMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isUser: false,
        timestamp: new Date(),
        confidence: Math.round(confidence * 100),
        relatedTopics,
        sources: sources.slice(0, Math.floor(Math.random() * 2) + 1),
        readTime: Math.ceil(response.split(' ').length / 200 * 60),
        hasActions: true,
        actions: generateMessageActions(response, text.trim()),
      };

      setMessages(prev => [...prev, enhancedMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'));

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I'm sorry, I couldn't process your question right now. Please try again.",
        isUser: false,
        timestamp: new Date(),
        confidence: 0,
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    setLoading(false);
    setIsTyping(false);

    // Scroll to bottom
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  // Generate related topics based on user question
  const generateRelatedTopics = (question: string): string[] => {
    const topicMap: {[key: string]: string[]} = {
      'protein': ['Muscle Building', 'Recovery', 'Amino Acids'],
      'weight': ['Calorie Deficit', 'Metabolism', 'Exercise'],
      'energy': ['Complex Carbs', 'B Vitamins', 'Iron'],
      'vitamin': ['Micronutrients', 'Deficiency', 'Supplements'],
      'meal': ['Meal Prep', 'Portion Control', 'Timing'],
      'sugar': ['Blood Sugar', 'Insulin', 'Cravings'],
      'fat': ['Healthy Fats', 'Omega-3', 'Cholesterol'],
      'fiber': ['Digestion', 'Gut Health', 'Satiety'],
    };

    const topics: string[] = [];
    Object.keys(topicMap).forEach(keyword => {
      if (question.toLowerCase().includes(keyword)) {
        topics.push(...topicMap[keyword]);
      }
    });

    return [...new Set(topics)].slice(0, 3);
  };

  // Handle message reactions
  const handleMessageReaction = (messageId: string, reaction: 'helpful' | 'not-helpful') => {
    setMessageReactions(prev => ({
      ...prev,
      [messageId]: prev[messageId] === reaction ? null : reaction
    }));

    // Update message with reaction
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, isHelpful: reaction === 'helpful' }
        : msg
    ));
  };

  const buildContextPrompt = (question: string): string => {
    let contextInfo = '';
    
    if (userContext.recentScans.length > 0) {
      contextInfo += `Recent meals scanned today: ${userContext.recentScans.join(', ')}. `;
    }
    
    if (userContext.dailyIntake) {
      contextInfo += `Today's intake so far: ${userContext.dailyIntake.totalNutrition.calories} calories, ${userContext.dailyIntake.totalNutrition.protein}g protein. `;
    }
    
    if (userContext.recentQuestions.length > 0) {
      contextInfo += `Recent questions: ${userContext.recentQuestions.slice(0, 2).join(', ')}. `;
    }

    return contextInfo ? `Context: ${contextInfo}\n\nQuestion: ${question}` : question;
  };

  const generateMessageActions = (response: string, question: string): MessageAction[] => {
    const actions: MessageAction[] = [];
    
    // If response mentions recipes, add recipe search action
    if (response.toLowerCase().includes('recipe') || question.toLowerCase().includes('recipe')) {
      actions.push({
        id: 'find-recipe',
        label: 'Find Recipes',
        icon: 'restaurant',
        action: () => {
          // Navigate to recipes screen
          Alert.alert('Recipe Search', 'Navigating to recipes...');
        },
      });
    }
    
    // If response mentions meal planning, add meal plan action
    if (response.toLowerCase().includes('meal plan') || response.toLowerCase().includes('planning')) {
      actions.push({
        id: 'create-plan',
        label: 'Create Plan',
        icon: 'calendar',
        action: () => {
          Alert.alert('Meal Planning', 'Navigating to meal planner...');
        },
      });
    }
    
    // If response mentions scanning or food analysis
    if (response.toLowerCase().includes('scan') || response.toLowerCase().includes('analyze')) {
      actions.push({
        id: 'scan-food',
        label: 'Scan Food',
        icon: 'camera',
        action: () => {
          Alert.alert('Food Scanner', 'Navigating to scanner...');
        },
      });
    }

    return actions;
  };

  const addBotMessage = (text: string, actions?: MessageAction[]) => {
    const botMessage: Message = {
      id: Date.now().toString(),
      text,
      isUser: false,
      timestamp: new Date(),
      hasActions: actions && actions.length > 0,
      actions,
    };

    setMessages(prev => [...prev, botMessage]);
    
    // Scroll to bottom
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText(result.text);
        setIsListening(false);
        setInputText(result.text);
        // Auto-send voice message
        setTimeout(() => {
          sendMessage(result.text);
          setVoiceText('');
        }, 500);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const MessageBubble: React.FC<{ message: Message; index: number }> = ({ message, index }) => {
    if (message.isTyping) {
      return (
        <Animated.View
          entering={FadeInLeft.delay(index * 100).duration(600)}
          style={[styles.messageBubble, styles.botBubble, styles.typingBubble]}
        >
          <View style={styles.typingIndicator}>
            <View style={[styles.typingDot, { animationDelay: '0ms' }]} />
            <View style={[styles.typingDot, { animationDelay: '150ms' }]} />
            <View style={[styles.typingDot, { animationDelay: '300ms' }]} />
          </View>
          <Text style={styles.typingText}>AI is thinking...</Text>
        </Animated.View>
      );
    }

    return (
      <Animated.View
        entering={FadeInLeft.delay(index * 100).duration(600)}
        style={[
          styles.messageBubble,
          message.isUser ? styles.userBubble : styles.botBubble,
        ]}
      >
        {/* Message content */}
        <Text style={[
          styles.messageText,
          message.isUser ? styles.userText : styles.botText,
        ]}>
          {message.text}
        </Text>

        {/* AI confidence and metadata for bot messages */}
        {!message.isUser && message.confidence && (
          <View style={styles.messageMetadata}>
            <View style={styles.confidenceContainer}>
              <Ionicons name="checkmark-circle" size={12} color={Colors.success} />
              <Text style={styles.confidenceText}>{message.confidence}% confident</Text>
            </View>
            {message.readTime && (
              <Text style={styles.readTimeText}>{message.readTime}s read</Text>
            )}
          </View>
        )}

        {/* Related topics */}
        {!message.isUser && message.relatedTopics && message.relatedTopics.length > 0 && (
          <View style={styles.relatedTopics}>
            <Text style={styles.relatedTopicsLabel}>Related:</Text>
            <View style={styles.topicTags}>
              {message.relatedTopics.map((topic, idx) => (
                <View key={idx} style={styles.topicTag}>
                  <Text style={styles.topicTagText}>{topic}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Sources */}
        {!message.isUser && message.sources && message.sources.length > 0 && (
          <View style={styles.sourcesContainer}>
            <Ionicons name="library-outline" size={12} color={Colors.mutedForeground} />
            <Text style={styles.sourcesText}>
              Sources: {message.sources.join(', ')}
            </Text>
          </View>
        )}

        {/* Message actions */}
        {message.hasActions && message.actions && (
          <View style={styles.messageActions}>
            {message.actions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.actionButton}
                onPress={action.action}
              >
                <Ionicons name={action.icon as any} size={16} color={Colors.brand} />
                <Text style={styles.actionButtonText}>{action.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Message reactions for bot messages */}
        {!message.isUser && (
          <View style={styles.messageReactions}>
            <TouchableOpacity
              style={[
                styles.reactionButton,
                messageReactions[message.id] === 'helpful' && styles.reactionButtonActive
              ]}
              onPress={() => handleMessageReaction(message.id, 'helpful')}
            >
              <Ionicons
                name={messageReactions[message.id] === 'helpful' ? "thumbs-up" : "thumbs-up-outline"}
                size={14}
                color={messageReactions[message.id] === 'helpful' ? Colors.success : Colors.mutedForeground}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.reactionButton,
                messageReactions[message.id] === 'not-helpful' && styles.reactionButtonActive
              ]}
              onPress={() => handleMessageReaction(message.id, 'not-helpful')}
            >
              <Ionicons
                name={messageReactions[message.id] === 'not-helpful' ? "thumbs-down" : "thumbs-down-outline"}
                size={14}
                color={messageReactions[message.id] === 'not-helpful' ? Colors.error : Colors.mutedForeground}
              />
            </TouchableOpacity>
          </View>
        )}

        {/* Timestamp */}
        <Text style={styles.messageTime}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Apple-Inspired Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              <Text style={styles.title}>Nutrition AI</Text>
              <Text style={styles.subtitle}>Your personal nutrition assistant</Text>
            </View>
            <View style={styles.headerIcon}>
              <View style={styles.headerIconBackground}>
                <Ionicons name="chatbubble-ellipses" size={20} color={Colors.brand} />
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map((message, index) => (
            <MessageBubble key={message.id} message={message} index={index} />
          ))}
          
          {loading && (
            <Animated.View entering={FadeInLeft.duration(300)} style={styles.loadingBubble}>
              <ActivityIndicator color={Colors.brand} />
              <Text style={styles.loadingText}>Thinking...</Text>
            </Animated.View>
          )}
        </ScrollView>

        {/* Quick Questions */}
        {messages.length <= 1 && (
          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.quickQuestions}>
            <Text style={styles.quickQuestionsTitle}>Quick Questions</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {quickQuestions.map((question) => (
                <TouchableOpacity
                  key={question.id}
                  style={styles.quickQuestionButton}
                  onPress={() => sendMessage(question.text)}
                >
                  <Ionicons name={question.icon as any} size={16} color={Colors.brand} />
                  <Text style={styles.quickQuestionText}>{question.text}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Animated.View>
        )}

        {/* Input Area */}
        <Animated.View entering={FadeInUp.delay(1000).duration(800)} style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me anything about nutrition..."
              placeholderTextColor={Colors.mutedForeground}
              value={inputText}
              onChangeText={handleInputChange}
              multiline
              maxLength={500}
              editable={!loading}
            />

            {/* Voice button - only show if voice is available */}
            {voiceAvailable ? (
              <TouchableOpacity
                style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
                onPress={isListening ? stopVoiceRecognition : startVoiceRecognition}
              >
                <Ionicons
                  name={isListening ? "stop" : "mic"}
                  size={20}
                  color={Colors.brandForeground}
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.voiceButton, styles.voiceButtonDisabled]}
                onPress={() => Alert.alert(
                  'Voice Recognition Not Available',
                  'To use REAL voice recognition:\n\n1. Open in web browser (Press "w" in Expo CLI)\n2. Use Chrome, Safari, or Edge\n3. Or build native app with EAS Build'
                )}
              >
                <Ionicons
                  name="mic-off"
                  size={20}
                  color={Colors.mutedForeground}
                />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.sendButton, (!inputText.trim() || loading) && styles.sendButtonDisabled]}
              onPress={() => sendMessage(inputText)}
              disabled={!inputText.trim() || loading}
              activeOpacity={0.8}
            >
              <View style={styles.sendButtonBackground}>
                <Ionicons name="send" size={18} color={inputText.trim() && !loading ? Colors.brand : Colors.mutedForeground} />
              </View>
            </TouchableOpacity>
          </View>

          {/* Voice feedback text */}
          {voiceText && (
            <Animated.View entering={FadeInUp.duration(300)} style={styles.voiceTextContainer}>
              <Text style={styles.voiceText}>{voiceText}</Text>
            </Animated.View>
          )}

          {/* Smart suggestions dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <Animated.View entering={FadeInUp.duration(200)} style={styles.suggestionsContainer}>
              {suggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionItem}
                  onPress={() => {
                    setInputText(suggestion);
                    setSuggestions([]);
                    setShowSuggestions(false);
                  }}
                >
                  <Ionicons name="bulb-outline" size={16} color={Colors.brand} />
                  <Text style={styles.suggestionText}>{suggestion}</Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          )}
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  // Apple-Inspired Header
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xxxl,
    paddingBottom: Spacing.xxl,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
    lineHeight: 24,
  },
  headerIcon: {
    marginLeft: Spacing.lg,
  },
  headerIconBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  messagesContainer: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  messagesContent: {
    paddingBottom: Spacing.lg,
  },
  messageBubble: {
    maxWidth: '85%',
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  userBubble: {
    alignSelf: 'flex-end',
    backgroundColor: Colors.brand,
  },
  botBubble: {
    alignSelf: 'flex-start',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  messageText: {
    fontSize: FontSizes.base,
    lineHeight: 20,
  },
  userText: {
    color: Colors.brandForeground,
  },
  botText: {
    color: Colors.foreground,
  },
  messageActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginTop: Spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    gap: Spacing.xs,
  },
  actionButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  messageTime: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginTop: Spacing.sm,
    textAlign: 'right',
  },
  loadingBubble: {
    alignSelf: 'flex-start',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  loadingText: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
  },
  quickQuestions: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  quickQuestionsTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  quickQuestionButton: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    maxWidth: 200,
  },
  quickQuestionText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    flexShrink: 1,
  },
  inputContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  textInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    maxHeight: 100,
    minHeight: 40,
    textAlignVertical: 'center',
  },
  voiceButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButtonActive: {
    backgroundColor: Colors.error,
  },
  voiceButtonDisabled: {
    backgroundColor: Colors.muted,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  voiceTextContainer: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.md,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Ultra Modern UI Styles
  suggestionsContainer: {
    marginTop: Spacing.sm,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    maxHeight: 200,
    ...Shadows.md,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderLight,
    gap: Spacing.md,
  },
  suggestionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    fontWeight: FontWeights.medium,
  },

  // Typing indicator
  typingBubble: {
    minHeight: 50,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: Spacing.xs,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.brand,
    opacity: 0.4,
  },
  typingText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
  },

  // Message metadata
  messageMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.xs,
    paddingTop: Spacing.xs,
    borderTopWidth: 1,
    borderTopColor: Colors.border + '30',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  confidenceText: {
    fontSize: FontSizes.xs,
    color: Colors.success,
    fontWeight: FontWeights.medium,
  },
  readTimeText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },

  // Related topics
  relatedTopics: {
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border + '30',
  },
  relatedTopicsLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
    fontWeight: FontWeights.medium,
  },
  topicTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
  },
  topicTag: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  topicTagText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },

  // Sources
  sourcesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
    gap: Spacing.xs,
  },
  sourcesText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
    flex: 1,
  },

  // Message reactions
  messageReactions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
    marginTop: Spacing.sm,
  },
  reactionButton: {
    padding: Spacing.xs,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.muted + '50',
  },
  reactionButtonActive: {
    backgroundColor: Colors.brandMuted,
  },
});

export default AskScreenEnhanced;
