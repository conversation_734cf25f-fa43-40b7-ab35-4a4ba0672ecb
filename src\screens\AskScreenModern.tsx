import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
  StatusBar,
  Pressable,
  Haptics,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
  ZoomOut,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput } from '../components/ModernInput';
import { ModernLoading } from '../components/ModernLoading';
import ApiService from '../services/ApiService';
import ScanHistoryService from '../services/ScanHistoryService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

const { width, height } = Dimensions.get('window');

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  typing?: boolean;
  suggestions?: string[];
}

interface MessageBubbleProps {
  message: ChatMessage;
  delay: number;
  onSuggestionPress?: (suggestion: string) => void;
}

interface QuickQuestionProps {
  question: {
    id: string;
    text: string;
    icon: keyof typeof Ionicons.glyphMap;
    category?: string;
  };
  onPress: () => void;
  index: number;
}

interface VoiceInputProps {
  isListening: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  voiceText: string;
}

interface TypingIndicatorProps {
  visible: boolean;
}

// Modern Typing Indicator Component
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ visible }) => {
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      dot1.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 600 }),
          withTiming(0, { duration: 600 })
        ),
        -1,
        false
      );
      dot2.value = withDelay(200, withRepeat(
        withSequence(
          withTiming(1, { duration: 600 }),
          withTiming(0, { duration: 600 })
        ),
        -1,
        false
      ));
      dot3.value = withDelay(400, withRepeat(
        withSequence(
          withTiming(1, { duration: 600 }),
          withTiming(0, { duration: 600 })
        ),
        -1,
        false
      ));
    }
  }, [visible]);

  const dot1Style = useAnimatedStyle(() => ({
    opacity: dot1.value,
    transform: [{ scale: interpolate(dot1.value, [0, 1], [0.8, 1.2]) }],
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: dot2.value,
    transform: [{ scale: interpolate(dot2.value, [0, 1], [0.8, 1.2]) }],
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: dot3.value,
    transform: [{ scale: interpolate(dot3.value, [0, 1], [0.8, 1.2]) }],
  }));

  if (!visible) return null;

  return (
    <Animated.View entering={FadeInLeft.duration(300)} style={styles.typingIndicator}>
      <View style={styles.aiAvatar}>
        <Ionicons name="sparkles" size={16} color={Colors.brand} />
      </View>
      <View style={styles.typingBubble}>
        <View style={styles.typingDots}>
          <Animated.View style={[styles.typingDot, dot1Style]} />
          <Animated.View style={[styles.typingDot, dot2Style]} />
          <Animated.View style={[styles.typingDot, dot3Style]} />
        </View>
      </View>
    </Animated.View>
  );
};

// Voice Input Component
const VoiceInput: React.FC<VoiceInputProps> = ({
  isListening,
  onStartListening,
  onStopListening,
  voiceText,
}) => {
  const scale = useSharedValue(1);
  const pulseOpacity = useSharedValue(0);

  useEffect(() => {
    if (isListening) {
      scale.value = withRepeat(
        withSequence(
          withTiming(1.2, { duration: 800 }),
          withTiming(1, { duration: 800 })
        ),
        -1,
        false
      );
      pulseOpacity.value = withRepeat(
        withSequence(
          withTiming(0.7, { duration: 800 }),
          withTiming(0, { duration: 800 })
        ),
        -1,
        false
      );
    } else {
      scale.value = withTiming(1, { duration: 300 });
      pulseOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isListening]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  return (
    <View style={styles.voiceInputContainer}>
      {voiceText && (
        <Animated.View entering={SlideInUp.duration(300)} style={styles.voiceTextContainer}>
          <Text style={styles.voiceText}>{voiceText}</Text>
        </Animated.View>
      )}

      <Animated.View style={[styles.voiceButton, animatedStyle]}>
        <Animated.View style={[styles.voicePulse, pulseStyle]} />
        <TouchableOpacity
          style={[styles.voiceButtonInner, isListening && styles.voiceButtonListening]}
          onPress={isListening ? onStopListening : onStartListening}
          onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)}
        >
          <Ionicons
            name={isListening ? "stop" : "mic"}
            size={24}
            color={isListening ? Colors.error : Colors.brandForeground}
          />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

// Quick Question Component
const QuickQuestion: React.FC<QuickQuestionProps> = ({ question, onPress, index }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  };

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 100).duration(500)}
      style={[styles.quickQuestion, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.quickQuestionButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.quickQuestionIcon}>
          <Ionicons name={question.icon} size={20} color={Colors.brand} />
        </View>
        <Text style={styles.quickQuestionText}>{question.text}</Text>
        <Ionicons name="chevron-forward" size={16} color={Colors.mutedForeground} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const AskScreenModern: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');
  const scrollViewRef = useRef<ScrollView>(null);

  const quickQuestions = [
    { id: '1', text: 'How much protein should I eat daily?', icon: 'fitness' as const, category: 'Nutrition' },
    { id: '2', text: 'What are the best foods for energy?', icon: 'flash' as const, category: 'Energy' },
    { id: '3', text: 'How can I reduce sugar cravings?', icon: 'heart' as const, category: 'Health' },
    { id: '4', text: 'What vitamins do I need?', icon: 'medical' as const, category: 'Supplements' },
    { id: '5', text: 'How to meal prep effectively?', icon: 'time' as const, category: 'Planning' },
    { id: '6', text: 'Best foods for better sleep?', icon: 'moon' as const, category: 'Wellness' },
    { id: '7', text: 'How to build muscle with diet?', icon: 'barbell' as const, category: 'Fitness' },
    { id: '8', text: 'What foods boost metabolism?', icon: 'speedometer' as const, category: 'Weight' },
  ];

// Message Bubble Component
const MessageBubble: React.FC<MessageBubbleProps> = ({ message, delay, onSuggestionPress }) => {
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);

  useEffect(() => {
    scale.value = withDelay(delay, withSpring(1, { damping: 15, stiffness: 300 }));
    opacity.value = withDelay(delay, withTiming(1, { duration: 300 }));
  }, [delay]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <Animated.View style={[styles.messageContainer, animatedStyle]}>
      <View style={[
        styles.messageBubble,
        message.isUser ? styles.userBubble : styles.aiBubble
      ]}>
        {!message.isUser && (
          <View style={styles.aiAvatar}>
            <Ionicons name="sparkles" size={16} color={Colors.brand} />
          </View>
        )}

        <View style={[
          styles.messageContent,
          message.isUser ? styles.userMessageContent : styles.aiMessageContent
        ]}>
          {message.isUser ? (
            <View style={styles.userMessage}>
              <Text style={styles.userMessageText}>{message.text}</Text>
            </View>
          ) : (
            <LinearGradient
              colors={[Colors.card, Colors.cardSecondary]}
              style={styles.aiMessage}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.aiMessageText}>{message.text}</Text>
            </LinearGradient>
          )}

          <Text style={[
            styles.messageTime,
            message.isUser ? styles.userMessageTime : styles.aiMessageTime
          ]}>
            {formatTime(message.timestamp)}
          </Text>
        </View>
      </View>

      {/* Suggestions */}
      {message.suggestions && message.suggestions.length > 0 && (
        <Animated.View
          entering={SlideInUp.delay(300).duration(500)}
          style={styles.suggestionsContainer}
        >
          {message.suggestions.map((suggestion, index) => (
            <TouchableOpacity
              key={index}
              style={styles.suggestionChip}
              onPress={() => onSuggestionPress?.(suggestion)}
            >
              <Text style={styles.suggestionText}>{suggestion}</Text>
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}
    </Animated.View>
  );
};

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setLoading(true);

    try {
      const response = await ApiService.askQuestion(text);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.answer,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error asking question:', error);
      // Fallback response
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: "I'm here to help with your nutrition questions! While I'm currently having trouble connecting to my knowledge base, I'd recommend consulting with a registered dietitian for personalized advice. In the meantime, focus on eating a balanced diet with plenty of fruits, vegetables, lean proteins, and whole grains.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    }
    setLoading(false);
  };

  const MessageBubble: React.FC<{ message: ChatMessage; delay: number }> = ({ message, delay }) => (
    <Animated.View 
      entering={FadeInUp.delay(delay).duration(400)}
      style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage
      ]}
    >
      {!message.isUser && (
        <View style={styles.aiAvatar}>
          <Ionicons name="nutrition" size={16} color={Colors.brand} />
        </View>
      )}
      <View style={[
        styles.messageContent,
        message.isUser ? styles.userMessageContent : styles.aiMessageContent
      ]}>
        <Text style={[
          styles.messageText,
          message.isUser ? styles.userMessageText : styles.aiMessageText
        ]}>
          {message.text}
        </Text>
        <Text style={[
          styles.messageTime,
          message.isUser ? styles.userMessageTime : styles.aiMessageTime
        ]}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </Animated.View>
  );

  const QuickQuestionCard: React.FC<{ question: any; delay: number }> = ({ question, delay }) => (
    <Animated.View entering={FadeInLeft.delay(delay).duration(600)}>
      <TouchableOpacity 
        style={styles.quickQuestionCard}
        onPress={() => sendMessage(question.text)}
      >
        <View style={styles.questionIcon}>
          <Ionicons name={question.icon as any} size={16} color={Colors.brand} />
        </View>
        <Text style={styles.questionText}>{question.text}</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const handleSuggestionPress = (suggestion: string) => {
    setInputText(suggestion);
  };

  const handleQuickQuestion = (question: string) => {
    sendMessage(question);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Modern Header */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
          <LinearGradient
            colors={[Colors.brand, Colors.brandSecondary]}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.headerContent}>
              <View style={styles.aiHeaderIcon}>
                <Ionicons name="sparkles" size={28} color={Colors.brandForeground} />
              </View>
              <View style={styles.headerText}>
                <Text style={styles.title}>AI Nutritionist</Text>
                <Text style={styles.subtitle}>Your personal nutrition expert</Text>
              </View>
              <View style={styles.headerActions}>
                <TouchableOpacity style={styles.headerButton}>
                  <Ionicons name="information-circle" size={24} color={Colors.brandForeground} />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Messages Container */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {messages.length === 0 ? (
            // Welcome Screen with Quick Questions
            <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.welcomeSection}>
              <View style={styles.welcomeCard}>
                <View style={styles.welcomeIcon}>
                  <Ionicons name="chatbubble-ellipses" size={40} color={Colors.brand} />
                </View>
                <Text style={styles.welcomeTitle}>Welcome to AI Nutritionist!</Text>
                <Text style={styles.welcomeDescription}>
                  Ask me anything about nutrition, diet, and healthy eating. I'm here to help you make informed decisions about your health.
                </Text>
              </View>

              <View style={styles.quickQuestionsSection}>
                <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
                <View style={styles.quickQuestionsGrid}>
                  {quickQuestions.map((question, index) => (
                    <QuickQuestion
                      key={question.id}
                      question={question}
                      onPress={() => handleQuickQuestion(question.text)}
                      index={index}
                    />
                  ))}
                </View>
              </View>

              <View style={styles.featuresSection}>
                <Text style={styles.featuresTitle}>I can help you with:</Text>
                <View style={styles.featuresList}>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Personalized nutrition advice</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Meal planning and recipes</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Dietary restrictions guidance</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Supplement recommendations</Text>
                  </View>
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <View style={styles.chatContainer}>
              {messages.map((message, index) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  delay={index * 100}
                  onSuggestionPress={handleSuggestionPress}
                />
              ))}
            </View>
          )}

          {/* Typing Indicator */}
          <TypingIndicator visible={loading} />
        </ScrollView>

        {/* Voice Input Overlay */}
        {isListening && (
          <VoiceInput
            isListening={isListening}
            onStartListening={startVoiceRecognition}
            onStopListening={stopVoiceRecognition}
            voiceText={voiceText}
          />
        )}

        {/* Input Section */}
        <Animated.View entering={SlideInUp.duration(600)} style={styles.inputSection}>
          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <TextInput
                style={styles.textInput}
                placeholder="Ask me anything about nutrition..."
                placeholderTextColor={Colors.mutedForeground}
                value={inputText}
                onChangeText={setInputText}
                multiline
                maxLength={500}
                onSubmitEditing={() => sendMessage(inputText)}
                blurOnSubmit={false}
              />

              <View style={styles.inputActions}>
                <TouchableOpacity
                  style={styles.voiceInputButton}
                  onPress={isListening ? stopVoiceRecognition : startVoiceRecognition}
                  onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)}
                >
                  <Ionicons
                    name={isListening ? "stop" : "mic"}
                    size={20}
                    color={isListening ? Colors.error : Colors.brand}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.sendButton, (!inputText.trim() || loading) && styles.sendButtonDisabled]}
                  onPress={() => sendMessage(inputText)}
                  disabled={!inputText.trim() || loading}
                  onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)}
                >
                  {loading ? (
                    <ModernLoading variant="spinner" size="sm" color={Colors.brandForeground} />
                  ) : (
                    <Ionicons name="send" size={20} color={Colors.brandForeground} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </View>
  );
};

              <View style={styles.featuresCard}>
                <Text style={styles.featuresTitle}>I can help you with:</Text>
                <View style={styles.featuresList}>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Nutrition facts and recommendations</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Meal planning and diet advice</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Healthy eating habits</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Food allergies and intolerances</Text>
                  </View>
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <View style={styles.chatContainer}>
              {messages.map((message, index) => (
                <MessageBubble 
                  key={message.id} 
                  message={message} 
                  delay={index * 100} 
                />
              ))}
              {loading && (
                <Animated.View entering={FadeInUp.duration(400)} style={styles.loadingMessage}>
                  <View style={styles.aiAvatar}>
                    <Ionicons name="nutrition" size={16} color={Colors.brand} />
                  </View>
                  <View style={styles.loadingContent}>
                    <ActivityIndicator size="small" color={Colors.brand} />
                    <Text style={styles.loadingText}>AI is thinking...</Text>
                  </View>
                </Animated.View>
              )}
            </View>
          )}
        </ScrollView>

        {/* Input Section */}
        <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.inputSection}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me anything about nutrition..."
              placeholderTextColor={Colors.mutedForeground}
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
            />
            <TouchableOpacity 
              style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
              onPress={() => sendMessage(inputText)}
              disabled={!inputText.trim() || loading}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={inputText.trim() ? Colors.brandForeground : Colors.mutedForeground} 
              />
            </TouchableOpacity>
          </View>
          
          {messages.length > 0 && (
            <TouchableOpacity 
              style={styles.clearButton}
              onPress={() => setMessages([])}
            >
              <Ionicons name="refresh" size={16} color={Colors.mutedForeground} />
              <Text style={styles.clearButtonText}>New Conversation</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardView: {
    flex: 1,
  },

  // Modern Header
  header: {
    marginBottom: Spacing.lg,
  },
  headerGradient: {
    paddingTop: 60, // Account for status bar
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.xl,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  aiHeaderIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    marginLeft: Spacing.lg,
  },
  title: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    opacity: 0.8,
  },
  headerActions: {
    marginLeft: Spacing.lg,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Messages Container
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  chatContainer: {
    gap: Spacing.lg,
  },

  // Welcome Section
  welcomeSection: {
    paddingVertical: Spacing.xl,
  },
  welcomeCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xxl,
    padding: Spacing.xxxl,
    alignItems: 'center',
    marginBottom: Spacing.xxl,
    ...Shadows.lg,
  },
  welcomeIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  welcomeTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  welcomeDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 24,
  },

  // Quick Questions
  quickQuestionsSection: {
    marginBottom: Spacing.xxl,
  },
  quickQuestionsTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  quickQuestionsGrid: {
    gap: Spacing.md,
  },
  quickQuestion: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  quickQuestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  quickQuestionIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  quickQuestionText: {
    flex: 1,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },

  // Features Section
  featuresSection: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
  },
  featuresTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  featuresList: {
    gap: Spacing.md,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  featureText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Quick Questions
  quickQuestionsSection: {
    flex: 1,
  },
  quickQuestionsTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  quickQuestionsSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
  },
  quickQuestionsGrid: {
    gap: Spacing.sm,
    marginBottom: Spacing.xl,
  },
  quickQuestionCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  questionIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Features Card
  featuresCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  featuresTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  featuresList: {
    gap: Spacing.sm,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  featureText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Chat Messages
  chatContainer: {
    gap: Spacing.md,
  },
  messageBubble: {
    flexDirection: 'row',
    marginVertical: Spacing.xs,
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  aiMessage: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  userMessageContent: {
    backgroundColor: Colors.brand,
    borderBottomRightRadius: BorderRadius.sm,
  },
  aiMessageContent: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderBottomLeftRadius: BorderRadius.sm,
  },
  messageText: {
    fontSize: FontSizes.base,
    lineHeight: 20,
    marginBottom: Spacing.xs,
  },
  userMessageText: {
    color: Colors.brandForeground,
  },
  aiMessageText: {
    color: Colors.foreground,
  },
  messageTime: {
    fontSize: FontSizes.xs,
  },
  userMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  aiMessageTime: {
    color: Colors.mutedForeground,
  },

  // Loading Message
  loadingMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Spacing.xs,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  loadingText: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
  },

  // Input Section
  inputSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.background,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  textInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    maxHeight: 100,
    paddingVertical: Spacing.sm,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.muted,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.sm,
    gap: Spacing.xs,
  },
  clearButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Modern Input Section
  inputSection: {
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  inputContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  textInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    maxHeight: 100,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.md,
  },
  inputActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  voiceInputButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.muted,
  },

  // Voice Input Overlay
  voiceInputContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  voiceTextContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    marginHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
    ...Shadows.lg,
  },
  voiceText: {
    fontSize: FontSizes.lg,
    color: Colors.foreground,
    textAlign: 'center',
  },
  voiceButton: {
    position: 'relative',
  },
  voicePulse: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.brand,
    top: -10,
    left: -10,
  },
  voiceButtonInner: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.xl,
  },
  voiceButtonListening: {
    backgroundColor: Colors.error,
  },

  // Message Bubbles
  messageContainer: {
    marginBottom: Spacing.lg,
  },
  messageBubble: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: Spacing.xs,
  },
  userBubble: {
    justifyContent: 'flex-end',
  },
  aiBubble: {
    justifyContent: 'flex-start',
  },
  messageContent: {
    maxWidth: '80%',
  },
  userMessageContent: {
    alignItems: 'flex-end',
  },
  aiMessageContent: {
    alignItems: 'flex-start',
  },
  userMessage: {
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.sm,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  userMessageText: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    lineHeight: 20,
  },
  aiMessage: {
    borderRadius: BorderRadius.xl,
    borderBottomLeftRadius: BorderRadius.sm,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  aiMessageText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: FontSizes.xs,
    marginTop: Spacing.xs,
    marginHorizontal: Spacing.md,
  },
  userMessageTime: {
    color: Colors.mutedForeground,
    textAlign: 'right',
  },
  aiMessageTime: {
    color: Colors.mutedForeground,
    textAlign: 'left',
  },

  // Suggestions
  suggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    marginTop: Spacing.md,
    marginLeft: 44, // Align with AI message
  },
  suggestionChip: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },

  // Typing Indicator
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: Spacing.lg,
  },
  typingBubble: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    borderBottomLeftRadius: BorderRadius.sm,
    padding: Spacing.lg,
    marginLeft: Spacing.md,
  },
  typingDots: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.mutedForeground,
  },
});

export default AskScreenModern;
