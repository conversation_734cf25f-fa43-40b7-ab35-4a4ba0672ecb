import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService from '../services/ApiService';
import ScanHistoryService from '../services/ScanHistoryService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const AskScreenModern: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);

  const quickQuestions = [
    { id: '1', text: 'How much protein should I eat daily?', icon: 'fitness' },
    { id: '2', text: 'What are the best foods for energy?', icon: 'flash' },
    { id: '3', text: 'How can I reduce sugar cravings?', icon: 'heart' },
    { id: '4', text: 'What vitamins do I need?', icon: 'medical' },
    { id: '5', text: 'How to meal prep effectively?', icon: 'time' },
    { id: '6', text: 'Best foods for better sleep?', icon: 'moon' },
  ];

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setLoading(true);

    try {
      const response = await ApiService.askQuestion(text);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.answer,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error asking question:', error);
      // Fallback response
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: "I'm here to help with your nutrition questions! While I'm currently having trouble connecting to my knowledge base, I'd recommend consulting with a registered dietitian for personalized advice. In the meantime, focus on eating a balanced diet with plenty of fruits, vegetables, lean proteins, and whole grains.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    }
    setLoading(false);
  };

  const MessageBubble: React.FC<{ message: ChatMessage; delay: number }> = ({ message, delay }) => (
    <Animated.View 
      entering={FadeInUp.delay(delay).duration(400)}
      style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage
      ]}
    >
      {!message.isUser && (
        <View style={styles.aiAvatar}>
          <Ionicons name="nutrition" size={16} color={Colors.brand} />
        </View>
      )}
      <View style={[
        styles.messageContent,
        message.isUser ? styles.userMessageContent : styles.aiMessageContent
      ]}>
        <Text style={[
          styles.messageText,
          message.isUser ? styles.userMessageText : styles.aiMessageText
        ]}>
          {message.text}
        </Text>
        <Text style={[
          styles.messageTime,
          message.isUser ? styles.userMessageTime : styles.aiMessageTime
        ]}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </Animated.View>
  );

  const QuickQuestionCard: React.FC<{ question: any; delay: number }> = ({ question, delay }) => (
    <Animated.View entering={FadeInLeft.delay(delay).duration(600)}>
      <TouchableOpacity 
        style={styles.quickQuestionCard}
        onPress={() => sendMessage(question.text)}
      >
        <View style={styles.questionIcon}>
          <Ionicons name={question.icon as any} size={16} color={Colors.brand} />
        </View>
        <Text style={styles.questionText}>{question.text}</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
          <Text style={styles.title}>Ask AI Nutritionist</Text>
          <Text style={styles.subtitle}>Get personalized nutrition advice</Text>
        </Animated.View>

        {/* Messages or Quick Questions */}
        <ScrollView 
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.length === 0 ? (
            // Quick Questions
            <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.quickQuestionsSection}>
              <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
              <Text style={styles.quickQuestionsSubtitle}>Tap any question to get started</Text>
              
              <View style={styles.quickQuestionsGrid}>
                {quickQuestions.map((question, index) => (
                  <QuickQuestionCard 
                    key={question.id} 
                    question={question} 
                    delay={600 + index * 100} 
                  />
                ))}
              </View>

              <View style={styles.featuresCard}>
                <Text style={styles.featuresTitle}>I can help you with:</Text>
                <View style={styles.featuresList}>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Nutrition facts and recommendations</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Meal planning and diet advice</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Healthy eating habits</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                    <Text style={styles.featureText}>Food allergies and intolerances</Text>
                  </View>
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <View style={styles.chatContainer}>
              {messages.map((message, index) => (
                <MessageBubble 
                  key={message.id} 
                  message={message} 
                  delay={index * 100} 
                />
              ))}
              {loading && (
                <Animated.View entering={FadeInUp.duration(400)} style={styles.loadingMessage}>
                  <View style={styles.aiAvatar}>
                    <Ionicons name="nutrition" size={16} color={Colors.brand} />
                  </View>
                  <View style={styles.loadingContent}>
                    <ActivityIndicator size="small" color={Colors.brand} />
                    <Text style={styles.loadingText}>AI is thinking...</Text>
                  </View>
                </Animated.View>
              )}
            </View>
          )}
        </ScrollView>

        {/* Input Section */}
        <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.inputSection}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me anything about nutrition..."
              placeholderTextColor={Colors.mutedForeground}
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
            />
            <TouchableOpacity 
              style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
              onPress={() => sendMessage(inputText)}
              disabled={!inputText.trim() || loading}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={inputText.trim() ? Colors.brandForeground : Colors.mutedForeground} 
              />
            </TouchableOpacity>
          </View>
          
          {messages.length > 0 && (
            <TouchableOpacity 
              style={styles.clearButton}
              onPress={() => setMessages([])}
            >
              <Ionicons name="refresh" size={16} color={Colors.mutedForeground} />
              <Text style={styles.clearButtonText}>New Conversation</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },

  // Messages Container
  messagesContainer: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  messagesContent: {
    paddingBottom: Spacing.lg,
  },

  // Quick Questions
  quickQuestionsSection: {
    flex: 1,
  },
  quickQuestionsTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  quickQuestionsSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
  },
  quickQuestionsGrid: {
    gap: Spacing.sm,
    marginBottom: Spacing.xl,
  },
  quickQuestionCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  questionIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Features Card
  featuresCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  featuresTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  featuresList: {
    gap: Spacing.sm,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  featureText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Chat Messages
  chatContainer: {
    gap: Spacing.md,
  },
  messageBubble: {
    flexDirection: 'row',
    marginVertical: Spacing.xs,
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  aiMessage: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  userMessageContent: {
    backgroundColor: Colors.brand,
    borderBottomRightRadius: BorderRadius.sm,
  },
  aiMessageContent: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderBottomLeftRadius: BorderRadius.sm,
  },
  messageText: {
    fontSize: FontSizes.base,
    lineHeight: 20,
    marginBottom: Spacing.xs,
  },
  userMessageText: {
    color: Colors.brandForeground,
  },
  aiMessageText: {
    color: Colors.foreground,
  },
  messageTime: {
    fontSize: FontSizes.xs,
  },
  userMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  aiMessageTime: {
    color: Colors.mutedForeground,
  },

  // Loading Message
  loadingMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Spacing.xs,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  loadingText: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
  },

  // Input Section
  inputSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.background,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  textInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    maxHeight: 100,
    paddingVertical: Spacing.sm,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.muted,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.sm,
    gap: Spacing.xs,
  },
  clearButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
});

export default AskScreenModern;
