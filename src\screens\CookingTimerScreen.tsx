import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';

const CookingTimerScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const recipeTitle = route.params?.recipeTitle || 'Cooking Timer';
  const recipeInstructions = route.params?.instructions || [];

  const [minutes, setMinutes] = useState(25);
  const [seconds, setSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [totalTime, setTotalTime] = useState(25 * 60);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(new Array(recipeInstructions.length).fill(false));

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRunning && (minutes > 0 || seconds > 0)) {
      interval = setInterval(() => {
        if (seconds > 0) {
          setSeconds(seconds - 1);
        } else if (minutes > 0) {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      }, 1000);
    } else if (minutes === 0 && seconds === 0 && isRunning) {
      setIsRunning(false);
      Alert.alert(
        'Timer Finished!',
        'Your cooking timer has finished. Check your food!',
        [{ text: 'OK', onPress: () => {} }]
      );
    }

    return () => clearInterval(interval);
  }, [isRunning, minutes, seconds]);

  const startTimer = () => {
    setIsRunning(true);
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setMinutes(Math.floor(totalTime / 60));
    setSeconds(totalTime % 60);
  };

  const completeStep = (stepIndex: number) => {
    const newCompletedSteps = [...completedSteps];
    newCompletedSteps[stepIndex] = true;
    setCompletedSteps(newCompletedSteps);

    // Auto-advance to next step
    if (stepIndex === currentStep && stepIndex < recipeInstructions.length - 1) {
      setCurrentStep(stepIndex + 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  const getStepDuration = (instruction: string): number => {
    // Extract time from instruction text (e.g., "Cook for 15 minutes")
    const timeMatch = instruction.match(/(\d+)\s*(minute|min|hour|hr)/i);
    if (timeMatch) {
      const value = parseInt(timeMatch[1]);
      const unit = timeMatch[2].toLowerCase();
      if (unit.includes('hour') || unit.includes('hr')) {
        return value * 60; // Convert hours to minutes
      }
      return value; // Already in minutes
    }
    return 5; // Default 5 minutes if no time specified
  };

  const setTimerForCurrentStep = () => {
    if (currentStep < recipeInstructions.length) {
      const duration = getStepDuration(recipeInstructions[currentStep]);
      setMinutes(duration);
      setSeconds(0);
      setTotalTime(duration * 60);
    }
  };

  const adjustTime = (minuteChange: number) => {
    if (!isRunning) {
      const newMinutes = Math.max(0, Math.min(99, minutes + minuteChange));
      setMinutes(newMinutes);
      setTotalTime(newMinutes * 60);
    }
  };

  const formatTime = (mins: number, secs: number) => {
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    const currentTime = minutes * 60 + seconds;
    return ((totalTime - currentTime) / totalTime) * 100;
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.foreground} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Cooking Timer</Text>
        <View style={styles.headerButton} />
      </Animated.View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Recipe Title */}
        <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.recipeSection}>
          <Text style={styles.recipeTitle}>{recipeTitle}</Text>
          <Text style={styles.recipeSubtitle}>Stay focused while cooking</Text>
        </Animated.View>

        {/* Timer Display */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.timerSection}>
          <View style={styles.timerContainer}>
            <View style={styles.progressRing}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    transform: [{ rotate: `${(getProgress() * 3.6)}deg` }],
                    opacity: isRunning ? 1 : 0.3 
                  }
                ]} 
              />
              <View style={styles.timerDisplay}>
                <Text style={styles.timerText}>{formatTime(minutes, seconds)}</Text>
                <Text style={styles.timerLabel}>
                  {isRunning ? 'Cooking...' : 'Ready to start'}
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Step-by-Step Instructions */}
        {recipeInstructions.length > 0 && (
          <Animated.View entering={FadeInUp.delay(500).duration(800)} style={styles.stepsSection}>
            <Text style={styles.stepsTitle}>Cooking Steps</Text>
            <View style={styles.currentStepCard}>
              <View style={styles.stepHeader}>
                <Text style={styles.stepNumber}>Step {currentStep + 1} of {recipeInstructions.length}</Text>
                <TouchableOpacity
                  style={styles.stepTimerButton}
                  onPress={setTimerForCurrentStep}
                >
                  <Ionicons name="timer" size={16} color={Colors.brand} />
                  <Text style={styles.stepTimerText}>Set Timer</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.currentStepText}>
                {recipeInstructions[currentStep] || 'All steps completed!'}
              </Text>
              {currentStep < recipeInstructions.length && (
                <TouchableOpacity
                  style={[
                    styles.completeStepButton,
                    completedSteps[currentStep] && styles.completeStepButtonDone
                  ]}
                  onPress={() => completeStep(currentStep)}
                >
                  <Ionicons
                    name={completedSteps[currentStep] ? "checkmark-circle" : "checkmark-circle-outline"}
                    size={20}
                    color={completedSteps[currentStep] ? Colors.brandForeground : Colors.brand}
                  />
                  <Text style={[
                    styles.completeStepText,
                    completedSteps[currentStep] && styles.completeStepTextDone
                  ]}>
                    {completedSteps[currentStep] ? 'Completed' : 'Mark Complete'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* All Steps Overview */}
            <View style={styles.allStepsContainer}>
              {recipeInstructions.map((instruction, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.stepItem,
                    index === currentStep && styles.stepItemActive,
                    completedSteps[index] && styles.stepItemCompleted
                  ]}
                  onPress={() => goToStep(index)}
                >
                  <View style={[
                    styles.stepIndicator,
                    index === currentStep && styles.stepIndicatorActive,
                    completedSteps[index] && styles.stepIndicatorCompleted
                  ]}>
                    {completedSteps[index] ? (
                      <Ionicons name="checkmark" size={12} color={Colors.brandForeground} />
                    ) : (
                      <Text style={[
                        styles.stepIndicatorText,
                        index === currentStep && styles.stepIndicatorTextActive
                      ]}>
                        {index + 1}
                      </Text>
                    )}
                  </View>
                  <Text style={[
                    styles.stepItemText,
                    index === currentStep && styles.stepItemTextActive,
                    completedSteps[index] && styles.stepItemTextCompleted
                  ]} numberOfLines={2}>
                    {instruction}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Animated.View>
        )}

        {/* Time Adjusters */}
        {!isRunning && (
          <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.adjustersSection}>
            <Text style={styles.adjustersTitle}>Adjust Time</Text>
            <View style={styles.adjustersContainer}>
              <TouchableOpacity 
                style={styles.adjusterButton}
                onPress={() => adjustTime(-5)}
              >
                <Text style={styles.adjusterButtonText}>-5 min</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.adjusterButton}
                onPress={() => adjustTime(-1)}
              >
                <Text style={styles.adjusterButtonText}>-1 min</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.adjusterButton}
                onPress={() => adjustTime(1)}
              >
                <Text style={styles.adjusterButtonText}>+1 min</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.adjusterButton}
                onPress={() => adjustTime(5)}
              >
                <Text style={styles.adjusterButtonText}>+5 min</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}

        {/* Control Buttons */}
        <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.controlsSection}>
          <TouchableOpacity 
            style={[styles.controlButton, styles.primaryControl]}
            onPress={isRunning ? pauseTimer : startTimer}
          >
            <Ionicons 
              name={isRunning ? "pause" : "play"} 
              size={24} 
              color={Colors.brandForeground} 
            />
            <Text style={styles.primaryControlText}>
              {isRunning ? 'Pause' : 'Start'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.controlButton, styles.secondaryControl]}
            onPress={resetTimer}
          >
            <Ionicons name="refresh" size={24} color={Colors.brand} />
            <Text style={styles.secondaryControlText}>Reset</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Quick Timers */}
        <Animated.View entering={FadeInUp.delay(1000).duration(800)} style={styles.quickTimersSection}>
          <Text style={styles.quickTimersTitle}>Quick Timers</Text>
          <View style={styles.quickTimersContainer}>
            {[5, 10, 15, 20].map((time) => (
              <TouchableOpacity 
                key={time}
                style={styles.quickTimer}
                onPress={() => {
                  setMinutes(time);
                  setSeconds(0);
                  setTotalTime(time * 60);
                  setIsRunning(false);
                }}
              >
                <Text style={styles.quickTimerText}>{time}m</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },

  // Header
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },

  // Recipe Section
  recipeSection: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  recipeTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  recipeSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },

  // Timer Section
  timerSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressRing: {
    width: 280,
    height: 280,
    borderRadius: 140,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '50%',
    height: '100%',
    backgroundColor: Colors.brand,
    transformOrigin: 'right center',
  },
  timerDisplay: {
    alignItems: 'center',
    zIndex: 1,
  },
  timerText: {
    fontSize: 48,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  timerLabel: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
  },

  // Adjusters
  adjustersSection: {
    marginBottom: Spacing.xl,
  },
  adjustersTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  adjustersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  adjusterButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  adjusterButtonText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },

  // Controls
  controlsSection: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginBottom: Spacing.xl,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  primaryControl: {
    backgroundColor: Colors.brand,
  },
  primaryControlText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },
  secondaryControl: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
  },
  secondaryControlText: {
    color: Colors.brand,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Quick Timers
  quickTimersSection: {
    marginBottom: Spacing.xl,
  },
  quickTimersTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  quickTimersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  quickTimer: {
    flex: 1,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  quickTimerText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Step-by-Step Styles
  stepsSection: {
    marginBottom: Spacing.xl,
  },
  stepsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  currentStepCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 2,
    borderColor: Colors.brand,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  stepNumber: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  stepTimerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    gap: Spacing.xs,
  },
  stepTimerText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  currentStepText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    lineHeight: 24,
    marginBottom: Spacing.md,
  },
  completeStepButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    gap: Spacing.sm,
    alignSelf: 'flex-start',
  },
  completeStepButtonDone: {
    backgroundColor: Colors.brand,
  },
  completeStepText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  completeStepTextDone: {
    color: Colors.brandForeground,
  },
  allStepsContainer: {
    gap: Spacing.sm,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    gap: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  stepItemActive: {
    borderColor: Colors.brand,
    backgroundColor: Colors.brandMuted,
  },
  stepItemCompleted: {
    backgroundColor: Colors.success + '20',
    borderColor: Colors.success,
  },
  stepIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepIndicatorActive: {
    backgroundColor: Colors.brand,
  },
  stepIndicatorCompleted: {
    backgroundColor: Colors.success,
  },
  stepIndicatorText: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.bold,
    color: Colors.mutedForeground,
  },
  stepIndicatorTextActive: {
    color: Colors.brandForeground,
  },
  stepItemText: {
    flex: 1,
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    lineHeight: 20,
  },
  stepItemTextActive: {
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  stepItemTextCompleted: {
    color: Colors.success,
  },
});

export default CookingTimerScreen;
