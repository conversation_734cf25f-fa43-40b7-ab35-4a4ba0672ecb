import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Vibration,
  Alert,
  Pressable,

} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeInUp, 
  FadeInDown, 
  FadeInLeft, 
  FadeInRight,
  SlideInUp,
  SlideInDown,
  ZoomIn,
  ZoomOut,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';

const { width, height } = Dimensions.get('window');

interface TimerDisplayProps {
  minutes: number;
  seconds: number;
  isRunning: boolean;
  progress: number;
  onPress: () => void;
}

interface StepNavigatorProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: boolean[];
  onStepPress: (step: number) => void;
}

interface QuickTimerProps {
  label: string;
  minutes: number;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  index: number;
}

// Modern Timer Display Component
const TimerDisplay: React.FC<TimerDisplayProps> = ({
  minutes,
  seconds,
  isRunning,
  progress,
  onPress,
}) => {
  const scale = useSharedValue(1);
  const pulseOpacity = useSharedValue(0);

  useEffect(() => {
    if (isRunning) {
      pulseOpacity.value = withRepeat(
        withSequence(
          withTiming(0.3, { duration: 1000 }),
          withTiming(0, { duration: 1000 })
        ),
        -1,
        false
      );
    } else {
      pulseOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isRunning]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
  };

  const formatTime = (mins: number, secs: number) => {
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Animated.View style={[styles.timerContainer, animatedStyle]}>
      <View style={styles.timerBackground}>
        <Animated.View style={[styles.timerPulse, pulseStyle]} />
        
        {/* Progress Ring */}
        <View style={styles.progressRing}>
          <View style={[styles.progressBackground]} />
          <View 
            style={[
              styles.progressFill,
              { 
                transform: [{ rotate: `${progress * 360}deg` }],
              }
            ]} 
          />
        </View>

        <Pressable style={styles.timerButton} onPress={handlePress}>
          <LinearGradient
            colors={isRunning ? [Colors.error, Colors.error + '80'] : [Colors.brand, Colors.brand + '80']}
            style={styles.timerGradient}
          >
            <Text style={styles.timerText}>{formatTime(minutes, seconds)}</Text>
            <Text style={styles.timerLabel}>
              {isRunning ? 'Tap to Pause' : 'Tap to Start'}
            </Text>
          </LinearGradient>
        </Pressable>
      </View>
    </Animated.View>
  );
};

// Step Navigator Component
const StepNavigator: React.FC<StepNavigatorProps> = ({
  currentStep,
  totalSteps,
  completedSteps,
  onStepPress,
}) => {
  return (
    <View style={styles.stepNavigator}>
      <Text style={styles.stepNavigatorTitle}>
        Step {currentStep + 1} of {totalSteps}
      </Text>
      
      <View style={styles.stepIndicators}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.stepIndicator,
              index === currentStep && styles.stepIndicatorActive,
              completedSteps[index] && styles.stepIndicatorCompleted,
            ]}
            onPress={() => onStepPress(index)}
          >
            {completedSteps[index] ? (
              <Ionicons name="checkmark" size={12} color={Colors.brandForeground} />
            ) : (
              <Text style={[
                styles.stepIndicatorText,
                index === currentStep && styles.stepIndicatorTextActive,
              ]}>
                {index + 1}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// Quick Timer Component
const QuickTimer: React.FC<QuickTimerProps> = ({ label, minutes, icon, onPress, index }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  return (
    <Animated.View 
      entering={ZoomIn.delay(index * 100).duration(500)}
      style={[styles.quickTimer, animatedStyle]}
    >
      <Pressable style={styles.quickTimerButton} onPress={handlePress}>
        <View style={styles.quickTimerIcon}>
          <Ionicons name={icon} size={24} color={Colors.brand} />
        </View>
        <Text style={styles.quickTimerLabel}>{label}</Text>
        <Text style={styles.quickTimerTime}>{minutes} min</Text>
      </Pressable>
    </Animated.View>
  );
};

const CookingTimerScreenModern: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  
  const recipeTitle = (route.params as any)?.recipe || 'Cooking Timer';
  const instructions = (route.params as any)?.instructions || [];
  const servings = (route.params as any)?.servings || 4;

  const [minutes, setMinutes] = useState(15);
  const [seconds, setSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [totalTime, setTotalTime] = useState(15 * 60);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(
    new Array(instructions.length).fill(false)
  );
  const [showQuickTimers, setShowQuickTimers] = useState(false);
  const [showStepDetail, setShowStepDetail] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const quickTimers = [
    { label: 'Boil Water', minutes: 5, icon: 'water' as const },
    { label: 'Sauté', minutes: 8, icon: 'flame' as const },
    { label: 'Simmer', minutes: 15, icon: 'time' as const },
    { label: 'Bake', minutes: 25, icon: 'restaurant' as const },
    { label: 'Rest', minutes: 10, icon: 'pause' as const },
    { label: 'Chill', minutes: 30, icon: 'snow' as const },
  ];

  useEffect(() => {
    if (isRunning && (minutes > 0 || seconds > 0)) {
      intervalRef.current = setInterval(() => {
        if (seconds > 0) {
          setSeconds(seconds - 1);
        } else if (minutes > 0) {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    // Timer finished
    if (isRunning && minutes === 0 && seconds === 0) {
      setIsRunning(false);
      Vibration.vibrate([0, 500, 200, 500]);
      Alert.alert(
        'Timer Finished!',
        'Your cooking timer has completed.',
        [{ text: 'OK', onPress: () => {} }]
      );
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, minutes, seconds]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setMinutes(Math.floor(totalTime / 60));
    setSeconds(totalTime % 60);
  };

  const setQuickTimer = (mins: number) => {
    setMinutes(mins);
    setSeconds(0);
    setTotalTime(mins * 60);
    setIsRunning(false);
    setShowQuickTimers(false);
  };

  const nextStep = () => {
    if (currentStep < instructions.length - 1) {
      const newCompleted = [...completedSteps];
      newCompleted[currentStep] = true;
      setCompletedSteps(newCompleted);
      setCurrentStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const getProgress = () => {
    const elapsed = totalTime - (minutes * 60 + seconds);
    return totalTime > 0 ? elapsed / totalTime : 0;
  };

  const getCurrentInstruction = () => {
    return instructions[currentStep] || 'All steps completed!';
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.brand} />
      
      {/* Header */}
      <LinearGradient
        colors={[Colors.brand, Colors.brandSecondary]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.brandForeground} />
          </TouchableOpacity>
          
          <View style={styles.headerText}>
            <Text style={styles.headerTitle}>{recipeTitle}</Text>
            <Text style={styles.headerSubtitle}>Cooking for {servings} servings</Text>
          </View>
          
          <TouchableOpacity 
            style={styles.menuButton}
            onPress={() => setShowQuickTimers(true)}
          >
            <Ionicons name="timer" size={24} color={Colors.brandForeground} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Timer Display */}
        <Animated.View entering={ZoomIn.delay(200).duration(800)} style={styles.timerSection}>
          <TimerDisplay
            minutes={minutes}
            seconds={seconds}
            isRunning={isRunning}
            progress={getProgress()}
            onPress={toggleTimer}
          />

          <View style={styles.timerControls}>
            <ModernButton
              title="Reset"
              onPress={resetTimer}
              variant="outline"
              size="md"
              icon="refresh"
              style={styles.timerControlButton}
            />
            <ModernButton
              title="Quick Timers"
              onPress={() => setShowQuickTimers(true)}
              variant="secondary"
              size="md"
              icon="timer"
              style={styles.timerControlButton}
            />
          </View>
        </Animated.View>

        {/* Step Navigation */}
        {instructions.length > 0 && (
          <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.stepSection}>
            <StepNavigator
              currentStep={currentStep}
              totalSteps={instructions.length}
              completedSteps={completedSteps}
              onStepPress={goToStep}
            />
          </Animated.View>
        )}

        {/* Current Step */}
        {instructions.length > 0 && (
          <Animated.View entering={SlideInUp.delay(600).duration(600)} style={styles.currentStepSection}>
            <ModernCard variant="glass" title="" style={styles.currentStepCard}>
              <TouchableOpacity
                style={styles.stepDetailButton}
                onPress={() => setShowStepDetail(true)}
              >
                <View style={styles.stepHeader}>
                  <Text style={styles.stepTitle}>Current Step</Text>
                  <Ionicons name="expand" size={20} color={Colors.brand} />
                </View>

                <Text style={styles.currentStepText}>
                  {getCurrentInstruction()}
                </Text>

                <View style={styles.stepActions}>
                  <ModernButton
                    title="Previous"
                    onPress={previousStep}
                    variant="outline"
                    size="sm"
                    icon="chevron-back"
                    disabled={currentStep === 0}
                    style={styles.stepActionButton}
                  />
                  <ModernButton
                    title="Complete Step"
                    onPress={nextStep}
                    variant="primary"
                    size="sm"
                    icon="checkmark"
                    disabled={currentStep >= instructions.length - 1}
                    style={styles.stepActionButton}
                  />
                </View>
              </TouchableOpacity>
            </ModernCard>
          </Animated.View>
        )}

        {/* Progress Summary */}
        <Animated.View entering={SlideInUp.delay(800).duration(600)} style={styles.progressSection}>
          <ModernCard variant="default" title="" style={styles.progressCard}>
            <Text style={styles.progressTitle}>Cooking Progress</Text>
            <View style={styles.progressStats}>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {completedSteps.filter(Boolean).length}
                </Text>
                <Text style={styles.progressStatLabel}>Steps Done</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {instructions.length - completedSteps.filter(Boolean).length}
                </Text>
                <Text style={styles.progressStatLabel}>Remaining</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {Math.round((completedSteps.filter(Boolean).length / instructions.length) * 100)}%
                </Text>
                <Text style={styles.progressStatLabel}>Complete</Text>
              </View>
            </View>
          </ModernCard>
        </Animated.View>
      </View>

      {/* Quick Timers Modal */}
      <ModernModal
        visible={showQuickTimers}
        onClose={() => setShowQuickTimers(false)}
        title="Quick Timers"
        variant="fullscreen"
      >
        <View style={styles.quickTimersGrid}>
          {quickTimers.map((timer, index) => (
            <QuickTimer
              key={timer.label}
              label={timer.label}
              minutes={timer.minutes}
              icon={timer.icon}
              onPress={() => setQuickTimer(timer.minutes)}
              index={index}
            />
          ))}
        </View>
      </ModernModal>

      {/* Step Detail Modal */}
      <ModernModal
        visible={showStepDetail}
        onClose={() => setShowStepDetail(false)}
        title={`Step ${currentStep + 1} Details`}
        variant="center"
        size="lg"
      >
        <View style={styles.stepDetailContent}>
          <Text style={styles.stepDetailText}>
            {getCurrentInstruction()}
          </Text>

          <View style={styles.stepDetailActions}>
            <ModernButton
              title="Set Timer for Step"
              onPress={() => {
                setQuickTimer(15);
                setShowStepDetail(false);
              }}
              variant="primary"
              size="md"
              icon="timer"
              fullWidth
            />
          </View>
        </View>
      </ModernModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  // Header
  header: {
    paddingTop: 60, // Status bar
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.xl,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    marginHorizontal: Spacing.lg,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    opacity: 0.8,
    textAlign: 'center',
  },
  menuButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Main Content
  content: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
  },

  // Timer Section
  timerSection: {
    alignItems: 'center',
    marginBottom: Spacing.xxl,
  },
  timerContainer: {
    marginBottom: Spacing.xl,
  },
  timerBackground: {
    width: 280,
    height: 280,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerPulse: {
    position: 'absolute',
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: Colors.brand,
    top: -10,
    left: -10,
  },
  progressRing: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
  },
  progressBackground: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 8,
    borderColor: Colors.muted,
  },
  progressFill: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 8,
    borderColor: Colors.brand,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  timerButton: {
    width: 240,
    height: 240,
    borderRadius: 120,
    overflow: 'hidden',
    ...Shadows.xl,
  },
  timerGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    fontSize: FontSizes['4xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginBottom: Spacing.sm,
  },
  timerLabel: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    opacity: 0.8,
  },
  timerControls: {
    flexDirection: 'row',
    gap: Spacing.lg,
  },
  timerControlButton: {
    minWidth: 120,
  },

  // Step Navigation
  stepSection: {
    marginBottom: Spacing.xl,
  },
  stepNavigator: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  stepNavigatorTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepIndicatorActive: {
    backgroundColor: Colors.brand,
  },
  stepIndicatorCompleted: {
    backgroundColor: Colors.success,
  },
  stepIndicatorText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.mutedForeground,
  },
  stepIndicatorTextActive: {
    color: Colors.brandForeground,
  },

  // Current Step
  currentStepSection: {
    marginBottom: Spacing.xl,
  },
  currentStepCard: {
    padding: Spacing.xl,
  },
  stepDetailButton: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  stepTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  currentStepText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    lineHeight: 24,
    marginBottom: Spacing.xl,
  },
  stepActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  stepActionButton: {
    flex: 1,
  },

  // Progress Section
  progressSection: {
    marginBottom: Spacing.xl,
  },
  progressCard: {
    padding: Spacing.xl,
  },
  progressTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  progressStat: {
    alignItems: 'center',
  },
  progressStatValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brand,
    marginBottom: Spacing.xs,
  },
  progressStatLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },

  // Quick Timers
  quickTimersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.lg,
    padding: Spacing.xl,
    justifyContent: 'space-between',
  },
  quickTimer: {
    width: (width - Spacing.xl * 3 - Spacing.lg) / 2,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  quickTimerButton: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  quickTimerIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  quickTimerLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  quickTimerTime: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },

  // Step Detail Modal
  stepDetailContent: {
    padding: Spacing.xl,
  },
  stepDetailText: {
    fontSize: FontSizes.lg,
    color: Colors.foreground,
    lineHeight: 28,
    marginBottom: Spacing.xl,
    textAlign: 'center',
  },
  stepDetailActions: {
    gap: Spacing.md,
  },
});

export default CookingTimerScreenModern;
