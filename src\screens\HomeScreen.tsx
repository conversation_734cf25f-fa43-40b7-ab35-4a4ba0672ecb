import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft, FadeInRight, SlideInUp, ZoomIn } from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { userProfile } from '../constants/UserData';

const { width } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  delay?: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon, onPress, delay = 0 }) => {
  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(800)}>
      <TouchableOpacity
        style={styles.featureCard}
        onPress={onPress}
        activeOpacity={0.98}
      >
        <View style={styles.featureCardContent}>
          <View style={styles.featureIconContainer}>
            <View style={styles.featureIconBackground}>
              <Ionicons name={icon} size={20} color={Colors.brand} />
            </View>
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{title}</Text>
            <Text style={styles.featureDescription}>{description}</Text>
          </View>
          <View style={styles.featureArrowContainer}>
            <Ionicons name="chevron-forward" size={16} color={Colors.mutedForeground} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const features = [
    {
      title: 'Scan Meal',
      description: 'Instant analysis',
      icon: 'scan' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Scanner'),
    },
    {
      title: 'AI Recipes',
      description: 'Custom meals',
      icon: 'restaurant' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Recipes'),
    },
    {
      title: 'Meal Plan',
      description: 'Weekly diet',
      icon: 'calendar' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Plan'),
    },
    {
      title: 'Ask AI',
      description: 'Expert advice',
      icon: 'chatbubble-ellipses' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Ask'),
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Apple-Inspired Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              <Text style={styles.greeting}>
                Good morning, <Text style={styles.name}>{userProfile.name.split(' ')[0]}</Text>
              </Text>
              <Text style={styles.subtitle}>Ready to nourish your body today?</Text>
            </View>
            <View style={styles.headerIcon}>
              <View style={styles.headerIconBackground}>
                <Ionicons name="leaf" size={20} color={Colors.brand} />
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Apple-Inspired Progress Card */}
        <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.progressCard}>
          <View style={styles.progressCardContent}>
            <View style={styles.progressHeader}>
              <View style={styles.progressHeaderText}>
                <Text style={styles.progressTitle}>Today's Progress</Text>
                <Text style={styles.progressSubtitle}>Keep up the great work!</Text>
              </View>
              <View style={styles.progressIconContainer}>
                <View style={styles.progressIconBackground}>
                  <Ionicons name="trending-up" size={18} color={Colors.brand} />
                </View>
              </View>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <Animated.View
                  entering={FadeInLeft.delay(600).duration(1000)}
                  style={[
                    styles.progressBarFill,
                    { width: `${userProfile.dailyProgress}%` },
                  ]}
                />
              </View>
              <Text style={styles.progressPercentage}>{userProfile.dailyProgress}%</Text>
            </View>
          </View>
        </Animated.View>

        {/* Apple-Inspired Features Section */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.featuresContainer}>
          <View style={styles.featuresHeader}>
            <Text style={styles.featuresTitle}>Quick Actions</Text>
            <Text style={styles.featuresSubtitle}>Everything you need at your fingertips</Text>
          </View>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <FeatureCard
                key={feature.title}
                {...feature}
                delay={600 + index * 100}
              />
            ))}
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Base Container - Apple Clean
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
  },

  // Apple-Inspired Header
  header: {
    paddingTop: Spacing.xxxl,
    paddingBottom: Spacing.xxl,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  greeting: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
    letterSpacing: -0.5,
  },
  name: {
    color: Colors.brand,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
    lineHeight: 24,
  },
  headerIcon: {
    marginLeft: Spacing.lg,
  },
  headerIconBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Apple-Inspired Progress Card
  progressCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    marginBottom: Spacing.xxl,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  progressCardContent: {
    padding: Spacing.xl,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
  },
  progressHeaderText: {
    flex: 1,
  },
  progressTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  progressSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
  },
  progressIconContainer: {
    marginLeft: Spacing.lg,
  },
  progressIconBackground: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: 4,
    marginRight: Spacing.lg,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.brand,
    minWidth: 40,
    textAlign: 'right',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  progressTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.text,
  },
  progressSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.textLight,
  },
  progressIconContainer: {
    width: 64,
    height: 64,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primaryOpacity10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarContainer: {
    marginTop: Spacing.md,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: Colors.gray200,
    borderRadius: BorderRadius.full,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.full,
  },
  // Apple-Inspired Features Section
  featuresContainer: {
    paddingBottom: Spacing.xxxl,
  },
  featuresHeader: {
    marginBottom: Spacing.xl,
  },
  featuresTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
    letterSpacing: -0.3,
  },
  featuresSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
    lineHeight: 22,
  },
  featuresGrid: {
    gap: Spacing.md,
  },

  // Apple-Inspired Feature Cards
  featureCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  featureCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  featureIconContainer: {
    marginRight: Spacing.lg,
  },
  featureIconBackground: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
    letterSpacing: -0.2,
  },
  featureDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
    lineHeight: 20,
  },
  featureArrowContainer: {
    marginLeft: Spacing.lg,
  },
  featureArrow: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  featureTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  featureDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    lineHeight: 22,
  },
});

export default HomeScreen;
