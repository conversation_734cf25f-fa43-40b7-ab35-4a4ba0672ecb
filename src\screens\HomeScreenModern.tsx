import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  RefreshControl,
  ImageBackground,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  Easing,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { userProfile } from '../constants/UserData';
import ApiService from '../services/ApiService';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';

const { width, height } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  delay?: number;
  variant?: 'primary' | 'secondary' | 'glass' | 'gradient';
  backgroundImage?: any;
}

interface QuickActionProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
  color?: string;
  gradient?: string[];
  delay?: number;
}





// Stunning Quick Actions Component
const QuickAction: React.FC<QuickActionProps> = ({
  icon,
  label,
  onPress,
  color = Colors.brand,
  gradient = [Colors.brand, Colors.brand],
  delay = 0
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.92, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.25, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  return (
    <Animated.View entering={ZoomIn.delay(delay).duration(600)}>
      <Animated.View style={[styles.quickAction, animatedStyle, shadowStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.quickActionButton}
          activeOpacity={1}
        >
          <LinearGradient
            colors={[gradient[0], gradient[1]]}
            style={styles.quickActionIcon}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name={icon} size={26} color="white" />
          </LinearGradient>
          <Text style={styles.quickActionLabel}>{label}</Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};


// Enhanced Feature Card Component
const EnhancedFeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  onPress,
  delay = 0,
  variant = 'secondary',
  backgroundImage
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <Animated.View style={[styles.enhancedCard, animatedStyle, shadowAnimatedStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.enhancedCardButton}
          activeOpacity={1}
        >
          {variant === 'gradient' && (
            <LinearGradient
              colors={[Colors.brand, Colors.brandSecondary]}
              style={styles.enhancedCardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          )}

          {variant === 'glass' && (
            <View style={styles.enhancedCardGlass} />
          )}

          {backgroundImage && (
            <ImageBackground
              source={backgroundImage}
              style={styles.enhancedCardBackground}
              imageStyle={styles.enhancedCardBackgroundImage}
            />
          )}

          <View style={styles.enhancedCardContent}>
            <View style={[
              styles.enhancedCardIcon,
              variant === 'gradient' && styles.gradientCardIcon,
              variant === 'glass' && styles.glassCardIcon
            ]}>
              <Ionicons
                name={icon}
                size={28}
                color={variant === 'gradient' ? Colors.brandForeground : Colors.brand}
              />
            </View>

            <View style={styles.enhancedCardText}>
              <Text style={[
                styles.enhancedCardTitle,
                variant === 'gradient' && styles.gradientCardTitle
              ]}>
                {title}
              </Text>
              <Text style={[
                styles.enhancedCardDescription,
                variant === 'gradient' && styles.gradientCardDescription
              ]}>
                {description}
              </Text>
            </View>

            <View style={styles.enhancedCardArrow}>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={variant === 'gradient' ? Colors.brandForeground : Colors.mutedForeground}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};



const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1200);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const quickActions = [
    {
      icon: 'scan' as const,
      label: 'Smart Scan',
      onPress: () => (navigation as any).navigate('Scanner'),
      color: '#22C55E',
      gradient: ['#22C55E', '#16A34A']
    },
    {
      icon: 'restaurant' as const,
      label: 'AI Recipes',
      onPress: () => (navigation as any).navigate('Recipes'),
      color: '#16A34A',
      gradient: ['#16A34A', '#15803D']
    },
    {
      icon: 'calendar' as const,
      label: 'Meal Plan',
      onPress: () => (navigation as any).navigate('Plan'),
      color: '#15803D',
      gradient: ['#15803D', '#166534']
    },
    {
      icon: 'chatbubble-ellipses' as const,
      label: 'Ask AI',
      onPress: () => (navigation as any).navigate('Ask'),
      color: '#166534',
      gradient: ['#166534', '#14532D']
    },
  ];

  const statsData = [
    {
      title: 'Calories Today',
      value: '1,847',
      subtitle: 'of 2,200 goal',
      icon: 'flame' as const,
      color: Colors.warning,
    },
    {
      title: 'Water Intake',
      value: '8 glasses',
      subtitle: 'Great hydration!',
      icon: 'water' as const,
      color: Colors.info,
    },
    {
      title: 'Protein',
      value: '85g',
      subtitle: 'of 120g goal',
      icon: 'fitness' as const,
      color: Colors.success,
    },
  ];

  const featureCards = [
    {
      title: 'Smart Food Scanner',
      description: 'Instantly analyze nutrition with AI-powered scanning',
      icon: 'scan' as const,
      onPress: () => (navigation as any).navigate('Scanner'),
      variant: 'gradient' as const,
    },
    {
      title: 'Personalized Recipes',
      description: 'Discover recipes tailored to your dietary preferences',
      icon: 'restaurant' as const,
      onPress: () => (navigation as any).navigate('Recipes'),
      variant: 'glass' as const,
    },
    {
      title: 'Meal Planning',
      description: 'Plan your weekly meals with smart suggestions',
      icon: 'calendar' as const,
      onPress: () => (navigation as any).navigate('Plan'),
      variant: 'secondary' as const,
    },
  ];



  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#22c55e"
            colors={["#22c55e"]}
          />
        }
      >
        {/* Modern Shadcn Hero Card */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.heroCard}>
          <View style={styles.heroHeader}>
            <View>
              <Text style={styles.greetingText}>{getGreeting()}</Text>
              <Text style={styles.nameText}>{userProfile.name}</Text>
            </View>
            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => (navigation as any).navigate('Profile')}
            >
              <View style={styles.profileAvatar}>
                <Text style={styles.profileAvatarText}>{userProfile.name.charAt(0)}</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.progressSection}>
            <Text style={styles.progressLabel}>Daily Progress</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${userProfile.dailyProgress}%` }]} />
            </View>
            <Text style={styles.progressText}>{userProfile.dailyProgress}% of daily goal</Text>
          </View>

          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>1,847</Text>
              <Text style={styles.statLabel}>Calories</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>127g</Text>
              <Text style={styles.statLabel}>Protein</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>89g</Text>
              <Text style={styles.statLabel}>Carbs</Text>
            </View>
          </View>
        </Animated.View>

        {/* Modern Quick Actions */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <QuickAction
                key={action.label}
                icon={action.icon}
                label={action.label}
                onPress={action.onPress}
                color={action.color}
                gradient={action.gradient}
                delay={index * 150}
              />
            ))}
          </View>
        </Animated.View>



        {/* Feature Cards */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Explore Features</Text>

          {featureCards.map((card, index) => (
            <EnhancedFeatureCard
              key={card.title}
              title={card.title}
              description={card.description}
              icon={card.icon}
              onPress={card.onPress}
              variant={card.variant}
              delay={index * 150}
            />
          ))}
        </Animated.View>

        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Modern Shadcn Hero Card
  heroCard: {
    marginTop: 60,
    marginHorizontal: 16,
    marginBottom: 24,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  profileButton: {
    padding: 2,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileAvatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Progress Section
  progressSection: {
    marginBottom: 20,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#22c55e',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },

  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#22c55e',
    marginBottom: 4,
  },
  heroGradient: {
    padding: Spacing.xxxl,
    minHeight: 200,
    position: 'relative',
  },
  heroContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  greetingText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    marginBottom: 4,
  },
  nameText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  heroStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 197, 94, 0.12)',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(34, 197, 94, 0.2)',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 28,
    fontWeight: '800',
    color: '#16A34A',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#166534',
    letterSpacing: 0.5,
  },
  statDivider: {
    width: 2,
    height: 36,
    backgroundColor: 'rgba(34, 197, 94, 0.3)',
    marginHorizontal: 20,
    borderRadius: 1,
  },
  heroIcon: {
    position: 'absolute',
    top: Spacing.xl,
    right: Spacing.xl,
    opacity: 0.3,
  },

  // Billion-Dollar Quick Actions
  quickActionsSection: {
    marginHorizontal: 24,
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 26,
    fontWeight: '800',
    color: '#0F172A',
    marginBottom: 24,
    marginLeft: 4,
    letterSpacing: -0.5,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  quickAction: {
    flex: 1,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  quickActionButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    paddingVertical: 24,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: 'rgba(34, 197, 94, 0.1)',
  },
  quickActionIcon: {
    width: 64,
    height: 64,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  quickActionLabel: {
    fontSize: 14,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    letterSpacing: 0.3,
  },


  statsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsText: {
    flex: 1,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 4,
  },
  statsValue: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  statsSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  statsIcon: {
    width: 48,
    height: 48,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },


  // Enhanced Feature Cards
  featuresSection: {
    marginBottom: Spacing.xxl,
    paddingHorizontal: Spacing.xl,
  },
  enhancedCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.md,
  },
  enhancedCardButton: {
    position: 'relative',
  },
  enhancedCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  enhancedCardGlass: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.glass,
  },
  enhancedCardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  enhancedCardBackgroundImage: {
    opacity: 0.1,
    borderRadius: BorderRadius.xl,
  },
  enhancedCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.xl,
    backgroundColor: Colors.card,
    position: 'relative',
    zIndex: 2,
  },
  enhancedCardIcon: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.lg,
  },
  gradientCardIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  glassCardIcon: {
    backgroundColor: Colors.brandMuted,
  },
  enhancedCardText: {
    flex: 1,
  },
  enhancedCardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  gradientCardTitle: {
    color: Colors.brandForeground,
  },
  enhancedCardDescription: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  gradientCardDescription: {
    color: Colors.brandForeground,
    opacity: 0.8,
  },
  enhancedCardArrow: {
    marginLeft: Spacing.md,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  metricLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressBarBackground: {
    flex: 1,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },

  // Feature Cards Grid
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  // Modern Card Styles
  modernCard: {
    width: (width - Spacing.lg * 2 - Spacing.md) / 2,
    height: 160,
    borderRadius: BorderRadius.xl,
    marginBottom: Spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  primaryCard: {
    backgroundColor: Colors.brand,
  },
  secondaryCard: {
    backgroundColor: Colors.card,
  },
  cardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cardBackgroundImage: {
    opacity: 0.1,
    borderRadius: BorderRadius.xl,
  },
  cardContent: {
    flex: 1,
    padding: Spacing.md,
    justifyContent: 'space-between',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  secondaryIconContainer: {
    backgroundColor: Colors.brandMuted,
  },
  cardTextContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  cardTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    marginBottom: Spacing.xs,
  },
  primaryCardTitle: {
    color: Colors.brandForeground,
  },
  secondaryCardTitle: {
    color: Colors.foreground,
  },
  cardDescription: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.normal,
  },
  primaryCardDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  secondaryCardDescription: {
    color: Colors.mutedForeground,
  },

  // Activity Card Styles
  activityCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  activityTime: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Test Button
  testButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  testButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },

  // Progress Ring
  progressRing: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRingBackground: {
    position: 'absolute',
  },
  progressRingCenter: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressRingText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default HomeScreenModern;
