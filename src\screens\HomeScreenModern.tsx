import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInUp,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { userProfile } from '../constants/UserData';

const { width } = Dimensions.get('window');












const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1200);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const quickActions = [
    {
      icon: 'scan' as const,
      label: 'Smart Scan',
      onPress: () => (navigation as any).navigate('Scanner'),
    },
    {
      icon: 'restaurant' as const,
      label: 'AI Recipes',
      onPress: () => (navigation as any).navigate('Recipes'),
    },
    {
      icon: 'calendar' as const,
      label: 'Meal Plan',
      onPress: () => (navigation as any).navigate('Plan'),
    },
    {
      icon: 'chatbubble-ellipses' as const,
      label: 'Ask AI',
      onPress: () => (navigation as any).navigate('Ask'),
    },
  ];







  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#f0f4f0', '#e8f0e8', '#f8f9fa']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        <View style={styles.backgroundPattern} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#6B7C5A"
            colors={["#6B7C5A"]}
          />
        }
      >
        {/* Beautiful Main Card - Inspired by your design */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.mainCard}>
          {/* Header with greeting and profile */}
          <View style={styles.cardHeader}>
            <View style={styles.greetingSection}>
              <Text style={styles.greetingText}>{getGreeting()},</Text>
              <Text style={styles.nameText}>{userProfile.name}</Text>
            </View>
            <TouchableOpacity
              style={styles.profileAvatar}
              onPress={() => (navigation as any).navigate('Profile')}
            >
              <Text style={styles.avatarText}>
                {userProfile.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Daily Progress Section */}
          <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.progressSection}>
            <Text style={styles.progressTitle}>Daily Progress</Text>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBar}>
                <Animated.View
                  entering={SlideInLeft.delay(400).duration(1000)}
                  style={[styles.progressFill, { width: `${userProfile.dailyProgress}%` }]}
                />
              </View>
            </View>
          </Animated.View>

          {/* Nutrition Stats Grid */}
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.nutritionGrid}>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionLabel}>Calories</Text>
              <Text style={styles.nutritionValue}>1100</Text>
              <Text style={styles.nutritionUnit}>kcal</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionLabel}>Protein</Text>
              <Text style={styles.nutritionValue}>52</Text>
              <Text style={styles.nutritionUnit}>g</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionLabel}>Carbs</Text>
              <Text style={styles.nutritionValue}>150</Text>
              <Text style={styles.nutritionUnit}>g</Text>
            </View>
          </Animated.View>

          {/* Quick Actions Grid */}
          <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.actionsGrid}>
            {quickActions.map((action, index) => (
              <Animated.View
                key={action.label}
                entering={SlideInUp.delay(700 + index * 100).duration(500)}
                style={styles.actionItem}
              >
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={action.onPress}
                >
                  <View style={styles.actionIcon}>
                    <Ionicons name={action.icon} size={28} color="#6B7C5A" />
                  </View>
                  <Text style={styles.actionLabel}>{action.label}</Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </Animated.View>

          {/* Recent Activity */}
          <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.activitySection}>
            <Text style={styles.activityTitle}>Recent Activity</Text>
            <View style={styles.activityList}>
              <View style={styles.activityItem}>
                <View style={styles.activityDot} />
                <Text style={styles.activityText}>Breakfast logged</Text>
              </View>
              <View style={styles.activityItem}>
                <View style={styles.activityDot} />
                <Text style={styles.activityText}>Water goal reached</Text>
              </View>
            </View>
          </Animated.View>
        </Animated.View>



        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundGradient: {
    flex: 1,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.03,
    backgroundColor: 'transparent',
  },

  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Beautiful Main Card - Inspired by your design
  mainCard: {
    marginTop: 60,
    marginHorizontal: 20,
    marginBottom: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.1,
    shadowRadius: 40,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Header Section
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 32,
  },
  greetingSection: {
    flex: 1,
  },
  greetingText: {
    fontSize: 28,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  nameText: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.5,
  },
  profileAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },

  // Progress Section
  progressSection: {
    marginBottom: 32,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 16,
  },
  progressBarContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 12,
    backgroundColor: '#e2e8f0',
    borderRadius: 6,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 6,
  },

  // Nutrition Grid
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  nutritionItem: {
    alignItems: 'center',
    flex: 1,
  },
  nutritionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4a5568',
    marginBottom: 8,
  },
  nutritionValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 4,
    letterSpacing: -1,
  },
  nutritionUnit: {
    fontSize: 14,
    fontWeight: '500',
    color: '#718096',
  },

  // Actions Grid
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 32,
    gap: 16,
  },
  actionItem: {
    width: (width - 88) / 2, // Account for margins and gap
  },
  actionButton: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)',
  },
  actionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
    textAlign: 'center',
  },

  // Activity Section
  activitySection: {
    marginTop: 8,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 16,
  },
  activityList: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  activityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#6B7C5A',
    marginRight: 12,
  },
  activityText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4a5568',
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default HomeScreenModern;
