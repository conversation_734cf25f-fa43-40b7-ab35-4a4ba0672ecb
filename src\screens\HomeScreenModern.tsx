import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInRight,
  SlideInLeft,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { userProfile } from '../constants/UserData';
import ApiService from '../services/ApiService';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';

const { width, height } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  delay?: number;
  variant?: 'primary' | 'secondary' | 'glass' | 'gradient';
  backgroundImage?: any;
}

interface QuickActionProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
  color?: string;
  delay?: number;
}

interface StatsCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  delay?: number;
}

// Hero Section Component
const HeroSection: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <Animated.View entering={FadeInDown.duration(800)} style={styles.heroSection}>
      <LinearGradient
        colors={[Colors.brand, Colors.brandSecondary, Colors.brandTertiary]}
        style={styles.heroGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.heroContent}>
          <Animated.View entering={SlideInLeft.delay(200).duration(600)}>
            <Text style={styles.greetingText}>{getGreeting()}</Text>
            <Text style={styles.nameText}>{userProfile.name}</Text>
            <Text style={styles.motivationText}>Ready to nourish your body today?</Text>
          </Animated.View>

          <Animated.View entering={SlideInRight.delay(400).duration(600)} style={styles.heroStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userProfile.dailyProgress}%</Text>
              <Text style={styles.statLabel}>Daily Goal</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>1,847</Text>
              <Text style={styles.statLabel}>Calories</Text>
            </View>
          </Animated.View>
        </View>

        <Animated.View entering={ZoomIn.delay(600).duration(600)} style={styles.heroIcon}>
          <Ionicons name="nutrition" size={60} color={Colors.brandForeground} />
        </Animated.View>
      </LinearGradient>
    </Animated.View>
  );
};

// Quick Actions Component
const QuickAction: React.FC<QuickActionProps> = ({
  icon,
  label,
  onPress,
  color = Colors.brand,
  delay = 0
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  };

  return (
    <Animated.View entering={ZoomIn.delay(delay).duration(500)}>
      <Animated.View style={[styles.quickAction, animatedStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.quickActionButton}
          activeOpacity={1}
        >
          <View style={[styles.quickActionIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon} size={24} color={color} />
          </View>
          <Text style={styles.quickActionLabel}>{label}</Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};
// Stats Card Component
const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  delay = 0
}) => {
  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <View style={[styles.statsCard, { borderLeftColor: color }]}>
        <View style={styles.statsContent}>
          <View style={styles.statsText}>
            <Text style={styles.statsTitle}>{title}</Text>
            <Text style={[styles.statsValue, { color }]}>{value}</Text>
            <Text style={styles.statsSubtitle}>{subtitle}</Text>
          </View>
          <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon} size={28} color={color} />
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

// Enhanced Feature Card Component
const EnhancedFeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  onPress,
  delay = 0,
  variant = 'secondary',
  backgroundImage
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <Animated.View style={[styles.enhancedCard, animatedStyle, shadowAnimatedStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.enhancedCardButton}
          activeOpacity={1}
        >
          {variant === 'gradient' && (
            <LinearGradient
              colors={[Colors.brand, Colors.brandSecondary]}
              style={styles.enhancedCardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          )}

          {variant === 'glass' && (
            <View style={styles.enhancedCardGlass} />
          )}

          {backgroundImage && (
            <ImageBackground
              source={backgroundImage}
              style={styles.enhancedCardBackground}
              imageStyle={styles.enhancedCardBackgroundImage}
            />
          )}

          <View style={styles.enhancedCardContent}>
            <View style={[
              styles.enhancedCardIcon,
              variant === 'gradient' && styles.gradientCardIcon,
              variant === 'glass' && styles.glassCardIcon
            ]}>
              <Ionicons
                name={icon}
                size={28}
                color={variant === 'gradient' ? Colors.brandForeground : Colors.brand}
              />
            </View>

            <View style={styles.enhancedCardText}>
              <Text style={[
                styles.enhancedCardTitle,
                variant === 'gradient' && styles.gradientCardTitle
              ]}>
                {title}
              </Text>
              <Text style={[
                styles.enhancedCardDescription,
                variant === 'gradient' && styles.gradientCardDescription
              ]}>
                {description}
              </Text>
            </View>

            <View style={styles.enhancedCardArrow}>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={variant === 'gradient' ? Colors.brandForeground : Colors.mutedForeground}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

// Progress Ring Component
const ProgressRing: React.FC<{ progress: number; size: number; strokeWidth: number; color: string }> = ({
  progress,
  size,
  strokeWidth,
  color
}) => {

  return (
    <View style={[styles.progressRing, { width: size, height: size }]}>
      <View style={[styles.progressRingBackground, {
        width: size - strokeWidth,
        height: size - strokeWidth,
        borderRadius: (size - strokeWidth) / 2,
        borderWidth: strokeWidth,
        borderColor: Colors.muted
      }]} />
      <View style={styles.progressRingCenter}>
        <Text style={[styles.progressRingText, { color }]}>{progress}%</Text>
      </View>
    </View>
  );
};

const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  };

  const quickActions = [
    { icon: 'scan' as const, label: 'Scan', onPress: () => (navigation as any).navigate('Scanner'), color: Colors.brand },
    { icon: 'restaurant' as const, label: 'Recipes', onPress: () => (navigation as any).navigate('Recipes'), color: Colors.secondary },
    { icon: 'calendar' as const, label: 'Plan', onPress: () => (navigation as any).navigate('Plan'), color: Colors.success },
    { icon: 'chatbubble-ellipses' as const, label: 'Ask AI', onPress: () => (navigation as any).navigate('Ask'), color: Colors.info },
  ];

  const statsData = [
    {
      title: 'Calories Today',
      value: '1,847',
      subtitle: 'of 2,200 goal',
      icon: 'flame' as const,
      color: Colors.warning,
    },
    {
      title: 'Water Intake',
      value: '8 glasses',
      subtitle: 'Great hydration!',
      icon: 'water' as const,
      color: Colors.info,
    },
    {
      title: 'Protein',
      value: '85g',
      subtitle: 'of 120g goal',
      icon: 'fitness' as const,
      color: Colors.success,
    },
  ];

  const featureCards = [
    {
      title: 'Smart Food Scanner',
      description: 'Instantly analyze nutrition with AI-powered scanning',
      icon: 'scan' as const,
      onPress: () => (navigation as any).navigate('Scanner'),
      variant: 'gradient' as const,
    },
    {
      title: 'Personalized Recipes',
      description: 'Discover recipes tailored to your dietary preferences',
      icon: 'restaurant' as const,
      onPress: () => (navigation as any).navigate('Recipes'),
      variant: 'glass' as const,
    },
    {
      title: 'Meal Planning',
      description: 'Plan your weekly meals with smart suggestions',
      icon: 'calendar' as const,
      onPress: () => (navigation as any).navigate('Plan'),
      variant: 'secondary' as const,
    },
  ];

  const features = [
    {
      title: 'Scan Meal',
      description: 'Analyze your food with AI',
      icon: 'camera' as keyof typeof Ionicons.glyphMap,
      onPress: () => (navigation as any).navigate('Scanner'),
      variant: 'primary' as const,
    },
    {
      title: 'AI Recipes',
      description: 'Get personalized recipes',
      icon: 'restaurant' as keyof typeof Ionicons.glyphMap,
      onPress: () => (navigation as any).navigate('Recipes'),
      variant: 'secondary' as const,
    },
    {
      title: 'Meal Plan',
      description: 'Plan your weekly meals',
      icon: 'calendar' as keyof typeof Ionicons.glyphMap,
      onPress: () => (navigation as any).navigate('Plan'),
      variant: 'secondary' as const,
    },
    {
      title: 'Ask AI',
      description: 'Get nutrition advice',
      icon: 'chatbubble' as keyof typeof Ionicons.glyphMap,
      onPress: () => (navigation as any).navigate('Ask'),
      variant: 'secondary' as const,
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.brand} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.brand}
            colors={[Colors.brand]}
          />
        }
      >
        {/* Hero Section */}
        <HeroSection />

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <QuickAction
                key={action.label}
                icon={action.icon}
                label={action.label}
                onPress={action.onPress}
                color={action.color}
                delay={index * 100}
              />
            ))}
          </View>
        </Animated.View>

        {/* Stats Section */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Today's Overview</Text>
          <View style={styles.statsGrid}>
            {statsData.map((stat, index) => (
              <StatsCard
                key={stat.title}
                title={stat.title}
                value={stat.value}
                subtitle={stat.subtitle}
                icon={stat.icon}
                color={stat.color}
                delay={index * 100}
              />
            ))}
          </View>
        </Animated.View>

        {/* Feature Cards */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Explore Features</Text>

          {featureCards.map((card, index) => (
            <EnhancedFeatureCard
              key={card.title}
              title={card.title}
              description={card.description}
              icon={card.icon}
              onPress={card.onPress}
              variant={card.variant}
              delay={index * 150}
            />
          ))}
        </Animated.View>

        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Space for floating tab bar
  },

  // Hero Section
  heroSection: {
    marginHorizontal: Spacing.xl,
    marginTop: Spacing.xl,
    marginBottom: Spacing.xxl,
    borderRadius: BorderRadius.xxl,
    overflow: 'hidden',
    ...Shadows.lg,
  },
  heroGradient: {
    padding: Spacing.xxxl,
    minHeight: 200,
    position: 'relative',
  },
  heroContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  greetingText: {
    fontSize: FontSizes.lg,
    color: Colors.brandForeground,
    fontWeight: FontWeights.medium,
    opacity: 0.9,
  },
  nameText: {
    fontSize: FontSizes['4xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginTop: Spacing.xs,
    marginBottom: Spacing.md,
  },
  motivationText: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    fontWeight: FontWeights.normal,
    opacity: 0.8,
    marginBottom: Spacing.xl,
  },
  heroStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.brandForeground,
    opacity: 0.8,
    marginTop: Spacing.xs,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: Colors.brandForeground,
    opacity: 0.3,
    marginHorizontal: Spacing.xl,
  },
  heroIcon: {
    position: 'absolute',
    top: Spacing.xl,
    right: Spacing.xl,
    opacity: 0.3,
  },

  // Section Styles
  sectionTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    marginHorizontal: Spacing.xl,
  },

  // Quick Actions
  quickActionsSection: {
    marginBottom: Spacing.xxl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.xl,
  },
  quickAction: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  quickActionButton: {
    alignItems: 'center',
    width: '100%',
  },
  quickActionIcon: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.md,
    ...Shadows.sm,
  },
  quickActionLabel: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    textAlign: 'center',
  },

  // Stats Section
  statsSection: {
    marginBottom: Spacing.xxl,
  },
  statsGrid: {
    paddingHorizontal: Spacing.xl,
  },
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderLeftWidth: 4,
    ...Shadows.sm,
  },
  statsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsText: {
    flex: 1,
  },
  statsTitle: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  statsValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    marginBottom: Spacing.xs,
  },
  statsSubtitle: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  statsIcon: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Enhanced Feature Cards
  featuresSection: {
    marginBottom: Spacing.xxl,
    paddingHorizontal: Spacing.xl,
  },
  enhancedCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.md,
  },
  enhancedCardButton: {
    position: 'relative',
  },
  enhancedCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  enhancedCardGlass: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.glass,
  },
  enhancedCardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  enhancedCardBackgroundImage: {
    opacity: 0.1,
    borderRadius: BorderRadius.xl,
  },
  enhancedCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.xl,
    backgroundColor: Colors.card,
    position: 'relative',
    zIndex: 2,
  },
  enhancedCardIcon: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.lg,
  },
  gradientCardIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  glassCardIcon: {
    backgroundColor: Colors.brandMuted,
  },
  enhancedCardText: {
    flex: 1,
  },
  enhancedCardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  gradientCardTitle: {
    color: Colors.brandForeground,
  },
  enhancedCardDescription: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  gradientCardDescription: {
    color: Colors.brandForeground,
    opacity: 0.8,
  },
  enhancedCardArrow: {
    marginLeft: Spacing.md,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  metricLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressBarBackground: {
    flex: 1,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },

  // Feature Cards Grid
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  // Modern Card Styles
  modernCard: {
    width: (width - Spacing.lg * 2 - Spacing.md) / 2,
    height: 160,
    borderRadius: BorderRadius.xl,
    marginBottom: Spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  primaryCard: {
    backgroundColor: Colors.brand,
  },
  secondaryCard: {
    backgroundColor: Colors.card,
  },
  cardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cardBackgroundImage: {
    opacity: 0.1,
    borderRadius: BorderRadius.xl,
  },
  cardContent: {
    flex: 1,
    padding: Spacing.md,
    justifyContent: 'space-between',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  secondaryIconContainer: {
    backgroundColor: Colors.brandMuted,
  },
  cardTextContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  cardTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    marginBottom: Spacing.xs,
  },
  primaryCardTitle: {
    color: Colors.brandForeground,
  },
  secondaryCardTitle: {
    color: Colors.foreground,
  },
  cardDescription: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.normal,
  },
  primaryCardDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  secondaryCardDescription: {
    color: Colors.mutedForeground,
  },

  // Activity Card Styles
  activityCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  activityTime: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Test Button
  testButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  testButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },

  // Progress Ring
  progressRing: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRingBackground: {
    position: 'absolute',
  },
  progressRingCenter: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressRingText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default HomeScreenModern;
