import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft, FadeInRight } from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import { userProfile } from '../constants/UserData';
import ApiService from '../services/ApiService';

const { width } = Dimensions.get('window');

interface FeatureCardProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  delay?: number;
  variant?: 'primary' | 'secondary';
}

const ModernCard: React.FC<FeatureCardProps> = ({ 
  title, 
  description, 
  icon, 
  onPress, 
  delay = 0,
  variant = 'secondary'
}) => {
  const getCardImage = () => {
    try {
      switch (title) {
        case 'Scan Meal':
          return require('../../assets/images/card-food-scan.png');
        case 'AI Recipes':
          return require('../../assets/images/card-food-recipe.png');
        case 'Meal Plan':
          return require('../../assets/images/card-food-plan.png');
        case 'Ask AI':
          return require('../../assets/images/card-food-ask.png');
        default:
          return require('../../assets/images/card-food-bloom.png');
      }
    } catch (error) {
      return null; // Fallback if image doesn't exist
    }
  };

  const cardImage = getCardImage();

  return (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <TouchableOpacity 
        style={[
          styles.modernCard,
          variant === 'primary' ? styles.primaryCard : styles.secondaryCard
        ]} 
        onPress={onPress} 
        activeOpacity={0.7}
      >
        {cardImage && (
          <ImageBackground
            source={cardImage}
            style={styles.cardBackground}
            imageStyle={styles.cardBackgroundImage}
          />
        )}
        <View style={styles.cardContent}>
          <View style={[
            styles.iconContainer,
            variant === 'primary' ? styles.primaryIconContainer : styles.secondaryIconContainer
          ]}>
            <Ionicons 
              name={icon} 
              size={24} 
              color={variant === 'primary' ? Colors.brandForeground : Colors.brand} 
            />
          </View>
          <View style={styles.cardTextContainer}>
            <Text style={[
              styles.cardTitle,
              variant === 'primary' ? styles.primaryCardTitle : styles.secondaryCardTitle
            ]}>
              {title}
            </Text>
            <Text style={[
              styles.cardDescription,
              variant === 'primary' ? styles.primaryCardDescription : styles.secondaryCardDescription
            ]}>
              {description}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const ProgressCard: React.FC = () => {
  return (
    <Animated.View entering={FadeInUp.delay(200).duration(800)}>
      <View style={styles.progressCard}>
        <View style={styles.progressHeader}>
          <View>
            <Text style={styles.progressTitle}>Today's Progress</Text>
            <Text style={styles.progressSubtitle}>Keep up the great work!</Text>
          </View>
          <View style={styles.progressIconContainer}>
            <Ionicons name="trending-up" size={24} color={Colors.brand} />
          </View>
        </View>
        
        <View style={styles.progressMetrics}>
          <View style={styles.metric}>
            <Text style={styles.metricValue}>{userProfile.dailyProgress}%</Text>
            <Text style={styles.metricLabel}>Daily Goal</Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricValue}>1,847</Text>
            <Text style={styles.metricLabel}>Calories</Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricValue}>8</Text>
            <Text style={styles.metricLabel}>Glasses</Text>
          </View>
        </View>

        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <Animated.View
              entering={FadeInRight.delay(800).duration(1000)}
              style={[
                styles.progressBarFill,
                { width: `${userProfile.dailyProgress}%` },
              ]}
            />
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();

  const testGeminiAPI = async () => {
    try {
      console.log('🧪 Testing Gemini API directly...');

      // Test direct Gemini API call
      const GEMINI_API_KEY = 'AIzaSyA-EVzCpQwPpKCnsUe2CRqdWIo8LTIKVnM';
      const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

      const requestBody = {
        contents: [{
          parts: [{
            text: 'Hello! Can you tell me 3 benefits of eating vegetables? Please keep it brief.'
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 512,
        }
      };

      console.log('📡 Making direct request to Gemini...');

      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Raw API response:', result);

      if (result.candidates && result.candidates[0] && result.candidates[0].content) {
        const text = result.candidates[0].content.parts[0].text;
        console.log('✅ Gemini API works! Response:', text);
        alert(`✅ Gemini API is working!\n\nResponse: ${text.substring(0, 200)}...`);
      } else {
        console.error('❌ Unexpected response structure:', result);
        alert('❌ Unexpected response structure from Gemini API');
      }
    } catch (error) {
      console.error('❌ Gemini API test failed:', error);
      alert(`❌ Gemini API test failed:\n${error.message}`);
    }
  };

  const features = [
    {
      title: 'Scan Meal',
      description: 'Analyze your food with AI',
      icon: 'camera' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Scanner' as never),
      variant: 'primary' as const,
    },
    {
      title: 'AI Recipes',
      description: 'Get personalized recipes',
      icon: 'restaurant' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Recipes' as never),
      variant: 'secondary' as const,
    },
    {
      title: 'Meal Plan',
      description: 'Plan your weekly meals',
      icon: 'calendar' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Plan' as never),
      variant: 'secondary' as const,
    },
    {
      title: 'Ask AI',
      description: 'Get nutrition advice',
      icon: 'chatbubble' as keyof typeof Ionicons.glyphMap,
      onPress: () => navigation.navigate('Ask' as never),
      variant: 'secondary' as const,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Text style={styles.greeting}>
            Good morning, <Text style={styles.name}>{userProfile.name.split(' ')[0]}</Text>
          </Text>
          <Text style={styles.subtitle}>Ready to nourish your body today?</Text>
        </Animated.View>

        {/* Progress Card */}
        <ProgressCard />

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <ModernCard
                key={feature.title}
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                onPress={feature.onPress}
                delay={600 + index * 100}
                variant={feature.variant}
              />
            ))}
          </View>
        </Animated.View>

        {/* Test Gemini API Button */}
        <Animated.View entering={FadeInUp.delay(1000).duration(800)} style={styles.section}>
          <TouchableOpacity style={styles.testButton} onPress={testGeminiAPI}>
            <Ionicons name="flash" size={20} color={Colors.brandForeground} />
            <Text style={styles.testButtonText}>Test Gemini API</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Recent Activity */}
        <Animated.View entering={FadeInUp.delay(1200).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityCard}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Breakfast logged</Text>
                <Text style={styles.activityTime}>2 hours ago</Text>
              </View>
            </View>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="water" size={20} color={Colors.info} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Hydration goal reached</Text>
                <Text style={styles.activityTime}>4 hours ago</Text>
              </View>
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
  },
  greeting: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  name: {
    color: Colors.brand,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Progress Card Styles
  progressCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  progressTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  progressSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  progressIconContainer: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  metricLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressBarBackground: {
    flex: 1,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },

  // Feature Cards Grid
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  // Modern Card Styles
  modernCard: {
    width: (width - Spacing.lg * 2 - Spacing.md) / 2,
    height: 160,
    borderRadius: BorderRadius.xl,
    marginBottom: Spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  primaryCard: {
    backgroundColor: Colors.brand,
  },
  secondaryCard: {
    backgroundColor: Colors.card,
  },
  cardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cardBackgroundImage: {
    opacity: 0.1,
    borderRadius: BorderRadius.xl,
  },
  cardContent: {
    flex: 1,
    padding: Spacing.md,
    justifyContent: 'space-between',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  secondaryIconContainer: {
    backgroundColor: Colors.brandMuted,
  },
  cardTextContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  cardTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    marginBottom: Spacing.xs,
  },
  primaryCardTitle: {
    color: Colors.brandForeground,
  },
  secondaryCardTitle: {
    color: Colors.foreground,
  },
  cardDescription: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.normal,
  },
  primaryCardDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  secondaryCardDescription: {
    color: Colors.mutedForeground,
  },

  // Activity Card Styles
  activityCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  activityTime: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Test Button
  testButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  testButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },
});

export default HomeScreenModern;
