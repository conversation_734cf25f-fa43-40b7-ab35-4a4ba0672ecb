import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService, { WeekPlan, DayPlan } from '../services/ApiService';

interface MealCardProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  meal: string;
  color: string;
}

const MealCard: React.FC<MealCardProps> = ({ icon, title, meal, color }) => (
  <View style={styles.mealCard}>
    <View style={styles.mealHeader}>
      <Ionicons name={icon} size={20} color={color} />
      <Text style={styles.mealTitle}>{title}</Text>
    </View>
    <Text style={styles.mealText}>{meal}</Text>
  </View>
);

const PlanScreen: React.FC = () => {
  const [goal, setGoal] = useState('weight loss');
  const [plan, setPlan] = useState<WeekPlan | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeDay, setActiveDay] = useState('Monday');

  const goals = [
    'weight loss',
    'muscle gain',
    'maintenance',
    'energy boost',
    'better health'
  ];

  const handleGenerate = async () => {
    setLoading(true);
    setPlan(null);

    try {
      const result = await ApiService.generateMealPlan(goal);
      setPlan(result);
      setActiveDay(result.week[0]?.day || 'Monday');
    } catch (error) {
      Alert.alert('Error', 'Failed to generate meal plan. Please try again.');
      console.error('Meal plan generation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const currentDayPlan = plan?.week.find((d) => d.day === activeDay);

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.backgroundAlt]}
        style={styles.backgroundGradient}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
            <Text style={styles.title}>Weekly Meal Planner</Text>
            <Text style={styles.subtitle}>Generate a 7-day plan for your goal.</Text>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Select your goal:</Text>
            <View style={styles.goalContainer}>
              {goals.map((goalOption) => (
                <TouchableOpacity
                  key={goalOption}
                  style={[
                    styles.goalChip,
                    goal === goalOption && styles.goalChipActive,
                  ]}
                  onPress={() => setGoal(goalOption)}
                >
                  <Text style={[
                    styles.goalChipText,
                    goal === goalOption && styles.goalChipTextActive,
                  ]}>
                    {goalOption}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={[styles.generateButton, loading && styles.generateButtonDisabled]}
              onPress={handleGenerate}
              disabled={loading}
            >
              <LinearGradient
                colors={loading ? [Colors.gray400, Colors.gray500] : [Colors.primary, Colors.secondary]}
                style={styles.buttonGradient}
              >
                <Ionicons
                  name={loading ? "hourglass" : "calendar-outline"}
                  size={24}
                  color={Colors.white}
                />
                <Text style={styles.buttonText}>
                  {loading ? 'Generating...' : 'Generate Plan'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Week Plan Results */}
          {plan && (
            <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.planContainer}>
              {/* Day Selector */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.daySelector}>
                {plan.week.map((dayPlan) => (
                  <TouchableOpacity
                    key={dayPlan.day}
                    style={[
                      styles.dayButton,
                      activeDay === dayPlan.day && styles.dayButtonActive,
                    ]}
                    onPress={() => setActiveDay(dayPlan.day)}
                  >
                    <Text style={[
                      styles.dayButtonText,
                      activeDay === dayPlan.day && styles.dayButtonTextActive,
                    ]}>
                      {dayPlan.day.slice(0, 3)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              {/* Current Day Plan */}
              {currentDayPlan && (
                <Animated.View
                  key={activeDay}
                  entering={FadeInUp.duration(400)}
                  style={styles.dayPlanContainer}
                >
                  <Text style={styles.dayTitle}>{currentDayPlan.day}</Text>

                  <MealCard
                    icon="sunny"
                    title="Breakfast"
                    meal={currentDayPlan.meals.breakfast}
                    color="#F59E0B"
                  />
                  <MealCard
                    icon="partly-sunny"
                    title="Lunch"
                    meal={currentDayPlan.meals.lunch}
                    color="#F97316"
                  />
                  <MealCard
                    icon="moon"
                    title="Dinner"
                    meal={currentDayPlan.meals.dinner}
                    color="#8B5CF6"
                  />
                </Animated.View>
              )}
            </Animated.View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    textAlign: 'center',
  },
  inputContainer: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  inputLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  goalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.lg,
  },
  goalChip: {
    backgroundColor: Colors.gray100,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  goalChipActive: {
    backgroundColor: Colors.primary,
  },
  goalChipText: {
    fontSize: FontSizes.sm,
    color: Colors.text,
    textTransform: 'capitalize',
  },
  goalChipTextActive: {
    color: Colors.white,
    fontWeight: FontWeights.semibold,
  },
  generateButton: {
    width: '100%',
  },
  generateButtonDisabled: {
    opacity: 0.7,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  buttonText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
  planContainer: {
    marginBottom: Spacing.xxl,
  },
  daySelector: {
    marginBottom: Spacing.lg,
  },
  dayButton: {
    backgroundColor: Colors.gray100,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
  },
  dayButtonActive: {
    backgroundColor: Colors.primary,
  },
  dayButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.text,
    fontWeight: FontWeights.medium,
  },
  dayButtonTextActive: {
    color: Colors.white,
    fontWeight: FontWeights.bold,
  },
  dayPlanContainer: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  dayTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  mealCard: {
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  mealTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  mealText: {
    fontSize: FontSizes.sm,
    color: Colors.textLight,
    lineHeight: 20,
  },
});

export default PlanScreen;
