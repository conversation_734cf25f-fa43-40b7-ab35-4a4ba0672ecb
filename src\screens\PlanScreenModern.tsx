import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService from '../services/ApiService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

interface MealPlan {
  day: string;
  date: string;
  meals: {
    breakfast: { name: string; calories: number; time: string };
    lunch: { name: string; calories: number; time: string };
    dinner: { name: string; calories: number; time: string };
    snack?: { name: string; calories: number; time: string };
  };
  totalCalories: number;
  waterGoal: number;
  completed: boolean;
}

const PlanScreenModern: React.FC = () => {
  const [weeklyPlan, setWeeklyPlan] = useState<MealPlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showCustomOptions, setShowCustomOptions] = useState(false);

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  const availableTags = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
    { id: 'gluten-free', label: 'Gluten Free', icon: 'shield-checkmark' },
    { id: 'dairy-free', label: 'Dairy Free', icon: 'water' },
    { id: 'budget-friendly', label: 'Budget Friendly', icon: 'wallet' },
    { id: 'quick-meals', label: 'Quick Meals', icon: 'time' },
    { id: 'meal-prep', label: 'Meal Prep', icon: 'cube' },
  ];

  const toggleTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText('');
        setIsListening(false);
        setCustomPrompt(result.text);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const generateWeeklyPlan = async () => {
    setLoading(true);
    try {
      // Build the goal string with custom prompt and tags
      let goal = 'healthy eating';
      if (customPrompt.trim()) {
        goal = customPrompt.trim();
      }
      if (selectedTags.length > 0) {
        const tagLabels = selectedTags.map(tagId =>
          availableTags.find(tag => tag.id === tagId)?.label || tagId
        );
        goal += ` with focus on: ${tagLabels.join(', ')}`;
      }

      console.log('🍽️ Generating meal plan with goal:', goal);
      const result = await ApiService.generateMealPlan(goal);

      // Convert API response to our MealPlan format
      if (result && result.week && Array.isArray(result.week)) {
        const convertedPlan: MealPlan[] = result.week.map((day, index) => ({
          day: day.day,
          date: `Dec ${16 + index}`, // Mock dates
          meals: {
            breakfast: { name: day.meals.breakfast, calories: 300, time: '8:00 AM' },
            lunch: { name: day.meals.lunch, calories: 450, time: '12:30 PM' },
            dinner: { name: day.meals.dinner, calories: 400, time: '7:00 PM' },
          },
          totalCalories: 1150,
          waterGoal: 8,
          completed: index < 2 // Mark first 2 days as completed
        }));

        setWeeklyPlan(convertedPlan);
        console.log('✅ Meal plan generated successfully');
      } else {
        console.warn('⚠️ Invalid meal plan structure, using fallback');
        throw new Error('Invalid meal plan structure');
      }
    } catch (error) {
      console.error('❌ Error generating meal plan:', error);
      // Fallback to mock data
      const mockPlan: MealPlan[] = [
        {
          day: 'Monday',
          date: 'Dec 16',
          meals: {
            breakfast: { name: 'Overnight Oats with Berries', calories: 320, time: '8:00 AM' },
            lunch: { name: 'Quinoa Buddha Bowl', calories: 450, time: '12:30 PM' },
            dinner: { name: 'Grilled Salmon & Vegetables', calories: 380, time: '7:00 PM' },
            snack: { name: 'Greek Yogurt with Nuts', calories: 150, time: '3:00 PM' }
          },
          totalCalories: 1300,
          waterGoal: 8,
          completed: true
        },
        {
          day: 'Tuesday',
          date: 'Dec 17',
          meals: {
            breakfast: { name: 'Avocado Toast with Eggs', calories: 380, time: '8:00 AM' },
            lunch: { name: 'Mediterranean Wrap', calories: 420, time: '12:30 PM' },
            dinner: { name: 'Chicken Stir Fry', calories: 400, time: '7:00 PM' }
          },
          totalCalories: 1200,
          waterGoal: 8,
          completed: false
        },
        {
          day: 'Wednesday',
          date: 'Dec 18',
          meals: {
            breakfast: { name: 'Smoothie Bowl', calories: 290, time: '8:00 AM' },
            lunch: { name: 'Lentil Soup & Salad', calories: 380, time: '12:30 PM' },
            dinner: { name: 'Baked Cod with Sweet Potato', calories: 360, time: '7:00 PM' }
          },
          totalCalories: 1030,
          waterGoal: 8,
          completed: false
        }
      ];
      setWeeklyPlan(mockPlan);
    }
    setLoading(false);
  };

  const MealCard: React.FC<{ 
    meal: { name: string; calories: number; time: string }; 
    type: string; 
    icon: string;
  }> = ({ meal, type, icon }) => (
    <View style={styles.mealCard}>
      <View style={styles.mealHeader}>
        <View style={styles.mealIcon}>
          <Ionicons name={icon as any} size={16} color={Colors.brand} />
        </View>
        <View style={styles.mealInfo}>
          <Text style={styles.mealType}>{type}</Text>
          <Text style={styles.mealTime}>{meal.time}</Text>
        </View>
        <Text style={styles.mealCalories}>{meal.calories} cal</Text>
      </View>
      <Text style={styles.mealName}>{meal.name}</Text>
    </View>
  );

  const DayCard: React.FC<{ plan: MealPlan; delay: number }> = ({ plan, delay }) => (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <TouchableOpacity 
        style={[styles.dayCard, plan.completed && styles.dayCardCompleted]}
        onPress={() => setSelectedDay(selectedDay === plan.day ? null : plan.day)}
      >
        <View style={styles.dayHeader}>
          <View style={styles.dayInfo}>
            <Text style={styles.dayName}>{plan.day}</Text>
            <Text style={styles.dayDate}>{plan.date}</Text>
          </View>
          <View style={styles.dayStats}>
            <View style={styles.caloriesBadge}>
              <Text style={styles.caloriesText}>{plan.totalCalories} cal</Text>
            </View>
            {plan.completed && (
              <View style={styles.completedBadge}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              </View>
            )}
          </View>
        </View>

        {selectedDay === plan.day && (
          <Animated.View entering={FadeInDown.duration(400)} style={styles.dayDetails}>
            <MealCard meal={plan.meals.breakfast} type="Breakfast" icon="sunny" />
            <MealCard meal={plan.meals.lunch} type="Lunch" icon="restaurant" />
            <MealCard meal={plan.meals.dinner} type="Dinner" icon="moon" />
            {plan.meals.snack && (
              <MealCard meal={plan.meals.snack} type="Snack" icon="cafe" />
            )}
            
            <View style={styles.waterTracker}>
              <View style={styles.waterHeader}>
                <Ionicons name="water" size={16} color={Colors.info} />
                <Text style={styles.waterText}>Water Goal: {plan.waterGoal} glasses</Text>
              </View>
              <View style={styles.waterProgress}>
                {Array.from({ length: plan.waterGoal }, (_, i) => (
                  <View 
                    key={i} 
                    style={[
                      styles.waterGlass,
                      i < (plan.completed ? plan.waterGoal : Math.floor(plan.waterGoal * 0.6)) && styles.waterGlassFilled
                    ]} 
                  />
                ))}
              </View>
            </View>
          </Animated.View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );

  const QuickStats: React.FC = () => (
    <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.statsCard}>
      <Text style={styles.statsTitle}>This Week</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="flame" size={20} color={Colors.warning} />
          </View>
          <Text style={styles.statValue}>9,450</Text>
          <Text style={styles.statLabel}>Calories</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
          </View>
          <Text style={styles.statValue}>5/7</Text>
          <Text style={styles.statLabel}>Days Complete</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="water" size={20} color={Colors.info} />
          </View>
          <Text style={styles.statValue}>42/56</Text>
          <Text style={styles.statLabel}>Glasses</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="trending-up" size={20} color={Colors.brand} />
          </View>
          <Text style={styles.statValue}>85%</Text>
          <Text style={styles.statLabel}>On Track</Text>
        </View>
      </View>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
          <Text style={styles.title}>Meal Planner</Text>
          <Text style={styles.subtitle}>Your personalized weekly nutrition plan</Text>
        </Animated.View>

        {/* Quick Stats */}
        <QuickStats />

        {/* Custom Options Toggle */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.customToggleSection}>
          <TouchableOpacity
            style={styles.customToggle}
            onPress={() => setShowCustomOptions(!showCustomOptions)}
          >
            <Text style={styles.customToggleText}>Customize Plan</Text>
            <Ionicons
              name={showCustomOptions ? "chevron-up" : "chevron-down"}
              size={20}
              color={Colors.brand}
            />
          </TouchableOpacity>
        </Animated.View>

        {/* Custom Options */}
        {showCustomOptions && (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.customOptionsSection}>
            {/* Custom Prompt */}
            <View style={styles.customPromptSection}>
              <Text style={styles.customSectionTitle}>Custom Instructions</Text>
              <View style={styles.customPromptContainer}>
                <TextInput
                  style={styles.customPromptInput}
                  placeholder="e.g., I want high-protein meals for muscle building..."
                  placeholderTextColor={Colors.mutedForeground}
                  value={customPrompt}
                  onChangeText={setCustomPrompt}
                  multiline
                  maxLength={200}
                />

                {/* Voice button for custom prompt */}
                <TouchableOpacity
                  style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
                  onPress={isListening ? stopVoiceRecognition : startVoiceRecognition}
                >
                  <Ionicons
                    name={isListening ? "stop" : "mic"}
                    size={18}
                    color={Colors.brandForeground}
                  />
                </TouchableOpacity>
              </View>

              {/* Voice feedback text */}
              {voiceText && (
                <Animated.View entering={FadeInUp.duration(300)} style={styles.voiceTextContainer}>
                  <Text style={styles.voiceText}>{voiceText}</Text>
                </Animated.View>
              )}
            </View>

            {/* Tags Selection */}
            <View style={styles.tagsSection}>
              <Text style={styles.customSectionTitle}>Dietary Preferences</Text>
              <View style={styles.tagsContainer}>
                {availableTags.map((tag) => (
                  <TouchableOpacity
                    key={tag.id}
                    style={[
                      styles.tagChip,
                      selectedTags.includes(tag.id) && styles.tagChipSelected
                    ]}
                    onPress={() => toggleTag(tag.id)}
                  >
                    <Ionicons
                      name={tag.icon as any}
                      size={14}
                      color={selectedTags.includes(tag.id) ? Colors.brandForeground : Colors.brand}
                    />
                    <Text style={[
                      styles.tagChipText,
                      selectedTags.includes(tag.id) && styles.tagChipTextSelected
                    ]}>
                      {tag.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Clear Options */}
            {(customPrompt.trim() || selectedTags.length > 0) && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => {
                  setCustomPrompt('');
                  setSelectedTags([]);
                }}
              >
                <Ionicons name="refresh" size={16} color={Colors.mutedForeground} />
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        )}

        {/* Generate Plan Button */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.generateContainer}>
          <TouchableOpacity 
            style={[styles.generateButton, loading && styles.generateButtonDisabled]}
            onPress={generateWeeklyPlan}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.brandForeground} />
            ) : (
              <Ionicons name="calendar" size={20} color={Colors.brandForeground} />
            )}
            <Text style={styles.generateButtonText}>
              {loading ? 'Generating Plan...' : weeklyPlan.length > 0 ? 'Regenerate Plan' : 'Generate Weekly Plan'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Weekly Plan */}
        {weeklyPlan.length > 0 && (
          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.section}>
            <Text style={styles.sectionTitle}>Your Weekly Plan</Text>
            {weeklyPlan.map((plan, index) => (
              <DayCard 
                key={plan.day} 
                plan={plan} 
                delay={1000 + index * 150} 
              />
            ))}
          </Animated.View>
        )}

        {/* Tips Section */}
        <Animated.View entering={FadeInUp.delay(1400).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Planning Tips</Text>
          <View style={styles.tipsCard}>
            <View style={styles.tip}>
              <Ionicons name="bulb" size={16} color={Colors.brandSecondary} />
              <Text style={styles.tipText}>Prep ingredients on Sunday for easier weekday cooking</Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="time" size={16} color={Colors.brandSecondary} />
              <Text style={styles.tipText}>Stick to consistent meal times for better metabolism</Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="water" size={16} color={Colors.brandSecondary} />
              <Text style={styles.tipText}>Drink water 30 minutes before each meal</Text>
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Stats Card
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  statValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Generate Button
  generateContainer: {
    marginBottom: Spacing.xl,
  },
  generateButton: {
    backgroundColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    opacity: 0.6,
  },
  generateButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Day Cards
  dayCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dayCardCompleted: {
    borderColor: Colors.success,
    backgroundColor: Colors.brandMuted,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  dayDate: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  dayStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  caloriesBadge: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  caloriesText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  completedBadge: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Day Details
  dayDetails: {
    marginTop: Spacing.lg,
    paddingTop: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },

  // Meal Cards
  mealCard: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  mealIcon: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  mealInfo: {
    flex: 1,
  },
  mealType: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  mealTime: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  mealCalories: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  mealName: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    marginLeft: 32,
  },

  // Water Tracker
  waterTracker: {
    marginTop: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.md,
  },
  waterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  waterText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },
  waterProgress: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  waterGlass: {
    width: 16,
    height: 16,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.muted,
  },
  waterGlassFilled: {
    backgroundColor: Colors.info,
  },

  // Tips Section
  tipsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  tipText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 20,
  },

  // Custom Options
  customToggleSection: {
    marginBottom: Spacing.lg,
  },
  customToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    gap: Spacing.sm,
  },
  customToggleText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  customOptionsSection: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  customSectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Custom Prompt
  customPromptSection: {
    marginBottom: Spacing.lg,
  },
  customPromptContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
  },
  customPromptInput: {
    flex: 1,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  voiceButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  voiceButtonActive: {
    backgroundColor: Colors.error,
  },
  voiceTextContainer: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.md,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Tags
  tagsSection: {
    marginBottom: Spacing.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    gap: Spacing.xs,
  },
  tagChipSelected: {
    backgroundColor: Colors.brand,
  },
  tagChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  tagChipTextSelected: {
    color: Colors.brandForeground,
  },

  // Clear Button
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.xs,
  },
  clearButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
});

export default PlanScreenModern;
