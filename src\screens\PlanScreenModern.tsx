import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  Pressable,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  useAnimatedGestureHandler,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput } from '../components/ModernInput';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import ApiService from '../services/ApiService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

const { width, height } = Dimensions.get('window');

interface MealPlan {
  day: string;
  date: string;
  meals: {
    breakfast: { name: string; calories: number; time: string; id?: string; image?: string };
    lunch: { name: string; calories: number; time: string; id?: string; image?: string };
    dinner: { name: string; calories: number; time: string; id?: string; image?: string };
    snack?: { name: string; calories: number; time: string; id?: string; image?: string };
  };
  totalCalories: number;
  waterGoal: number;
  completed: boolean;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface CalendarDayProps {
  date: Date;
  isSelected: boolean;
  isToday: boolean;
  hasPlans: boolean;
  onPress: () => void;
  index: number;
}

interface MealCardProps {
  meal: {
    name: string;
    calories: number;
    time: string;
    id?: string;
    image?: string;
  };
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  onPress: () => void;
  onEdit: () => void;
  onDelete: () => void;
  index: number;
}

interface WeekViewProps {
  weeklyPlan: MealPlan[];
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

interface TimelineViewProps {
  dayPlan: MealPlan;
  onMealPress: (mealType: string, meal: any) => void;
}

// Modern Calendar Day Component
const CalendarDay: React.FC<CalendarDayProps> = ({
  date,
  isSelected,
  isToday,
  hasPlans,
  onPress,
  index,
}) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(isSelected ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    backgroundColor.value = withTiming(isSelected ? 0 : 1, { duration: 200 });
    onPress();
  };

  return (
    <Animated.View
      entering={FadeInUp.delay(index * 50).duration(400)}
      style={[styles.calendarDay, animatedStyle]}
    >
      <TouchableOpacity style={styles.calendarDayButton} onPress={handlePress}>
        <Text style={[
          styles.calendarDayText,
          isSelected && styles.calendarDayTextSelected,
          isToday && !isSelected && styles.calendarDayTextToday
        ]}>
          {date.getDate()}
        </Text>
        <Text style={[
          styles.calendarDayName,
          isSelected && styles.calendarDayNameSelected
        ]}>
          {date.toLocaleDateString('en', { weekday: 'short' })}
        </Text>
        {hasPlans && (
          <View style={[
            styles.planIndicator,
            isSelected && styles.planIndicatorSelected
          ]} />
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Meal Card Component
const MealCard: React.FC<MealCardProps> = ({
  meal,
  mealType,
  onPress,
  onEdit,
  onDelete,
  index,
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  const getMealIcon = () => {
    switch (mealType) {
      case 'breakfast': return 'sunny';
      case 'lunch': return 'restaurant';
      case 'dinner': return 'moon';
      case 'snack': return 'fast-food';
      default: return 'restaurant';
    }
  };

  const getMealColor = () => {
    switch (mealType) {
      case 'breakfast': return Colors.warning;
      case 'lunch': return Colors.success;
      case 'dinner': return Colors.info;
      case 'snack': return Colors.brand;
      default: return Colors.mutedForeground;
    }
  };

  return (
    <Animated.View
      entering={SlideInLeft.delay(index * 100).duration(500)}
      style={[styles.mealCard, animatedStyle, shadowAnimatedStyle]}
    >
      <TouchableOpacity
        style={styles.mealCardButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.mealCardHeader}>
          <View style={[styles.mealIcon, { backgroundColor: getMealColor() + '20' }]}>
            <Ionicons name={getMealIcon()} size={20} color={getMealColor()} />
          </View>
          <View style={styles.mealInfo}>
            <Text style={styles.mealType}>{mealType.charAt(0).toUpperCase() + mealType.slice(1)}</Text>
            <Text style={styles.mealTime}>{meal.time}</Text>
          </View>
          <View style={styles.mealActions}>
            <TouchableOpacity style={styles.actionButton} onPress={onEdit}>
              <Ionicons name="pencil" size={16} color={Colors.mutedForeground} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
              <Ionicons name="trash" size={16} color={Colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.mealContent}>
          <Text style={styles.mealName}>{meal.name}</Text>
          <View style={styles.mealStats}>
            <View style={styles.statItem}>
              <Ionicons name="flame" size={14} color={Colors.warning} />
              <Text style={styles.statText}>{meal.calories} cal</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Week View Component
const WeekView: React.FC<WeekViewProps> = ({ weeklyPlan, selectedDate, onDateSelect }) => {
  const getWeekDates = () => {
    const week = [];
    const startOfWeek = new Date(selectedDate);
    startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay());

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      week.push(date);
    }
    return week;
  };

  const weekDates = getWeekDates();
  const today = new Date();

  return (
    <Animated.View entering={FadeInDown.duration(600)} style={styles.weekView}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.weekViewContainer}
      >
        {weekDates.map((date, index) => {
          const isSelected = date.toDateString() === selectedDate.toDateString();
          const isToday = date.toDateString() === today.toDateString();
          const hasPlans = weeklyPlan.some(plan => plan.date === date.toDateString());

          return (
            <CalendarDay
              key={date.toDateString()}
              date={date}
              isSelected={isSelected}
              isToday={isToday}
              hasPlans={hasPlans}
              onPress={() => onDateSelect(date)}
              index={index}
            />
          );
        })}
      </ScrollView>
    </Animated.View>
  );
};

// Timeline View Component
const TimelineView: React.FC<TimelineViewProps> = ({ dayPlan, onMealPress }) => {
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'] as const;

  return (
    <Animated.View entering={SlideInUp.duration(600)} style={styles.timelineView}>
      <Text style={styles.timelineTitle}>Today's Meal Plan</Text>

      {mealTypes.map((mealType, index) => {
        const meal = dayPlan.meals[mealType];

        if (!meal && mealType === 'snack') return null;

        return (
          <View key={mealType} style={styles.timelineItem}>
            <View style={styles.timelineLine}>
              <View style={styles.timelineDot} />
              {index < mealTypes.length - 1 && <View style={styles.timelineConnector} />}
            </View>

            <View style={styles.timelineContent}>
              {meal ? (
                <MealCard
                  meal={meal}
                  mealType={mealType}
                  onPress={() => onMealPress(mealType, meal)}
                  onEdit={() => {}}
                  onDelete={() => {}}
                  index={index}
                />
              ) : (
                <Animated.View
                  entering={FadeInRight.delay(index * 100).duration(500)}
                  style={styles.emptyMealSlot}
                >
                  <TouchableOpacity
                    style={styles.addMealButton}
                    onPress={() => onMealPress(mealType, null)}
                  >
                    <Ionicons name="add" size={24} color={Colors.brand} />
                    <Text style={styles.addMealText}>Add {mealType}</Text>
                  </TouchableOpacity>
                </Animated.View>
              )}
            </View>
          </View>
        );
      })}
    </Animated.View>
  );
};

const PlanScreenModern: React.FC = () => {
  const [weeklyPlan, setWeeklyPlan] = useState<MealPlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showCustomOptions, setShowCustomOptions] = useState(false);
  const [viewMode, setViewMode] = useState<'week' | 'day' | 'timeline'>('week');
  const [showAddMeal, setShowAddMeal] = useState(false);
  const [selectedMealType, setSelectedMealType] = useState<string>('');
  const [selectedDay, setSelectedDay] = useState<string | null>(null);

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  const availableTags = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
    { id: 'gluten-free', label: 'Gluten Free', icon: 'shield-checkmark' },
    { id: 'dairy-free', label: 'Dairy Free', icon: 'water' },
    { id: 'budget-friendly', label: 'Budget Friendly', icon: 'wallet' },
    { id: 'quick-meals', label: 'Quick Meals', icon: 'time' },
    { id: 'meal-prep', label: 'Meal Prep', icon: 'cube' },
  ];

  const toggleTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText('');
        setIsListening(false);
        setCustomPrompt(result.text);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const generateWeeklyPlan = async () => {
    setLoading(true);
    try {
      // Build the goal string with custom prompt and tags
      let goal = 'healthy eating';
      if (customPrompt.trim()) {
        goal = customPrompt.trim();
      }
      if (selectedTags.length > 0) {
        const tagLabels = selectedTags.map(tagId =>
          availableTags.find(tag => tag.id === tagId)?.label || tagId
        );
        goal += ` with focus on: ${tagLabels.join(', ')}`;
      }

      console.log('🍽️ Generating meal plan with goal:', goal);
      const result = await ApiService.generateMealPlan(goal);

      // Convert API response to our MealPlan format
      if (result && result.week && Array.isArray(result.week)) {
        const convertedPlan: MealPlan[] = result.week.map((day, index) => ({
          day: day.day,
          date: `Dec ${16 + index}`, // Mock dates
          meals: {
            breakfast: { name: day.meals.breakfast, calories: 300, time: '8:00 AM' },
            lunch: { name: day.meals.lunch, calories: 450, time: '12:30 PM' },
            dinner: { name: day.meals.dinner, calories: 400, time: '7:00 PM' },
          },
          totalCalories: 1150,
          waterGoal: 8,
          completed: index < 2 // Mark first 2 days as completed
        }));

        setWeeklyPlan(convertedPlan);
        console.log('✅ Meal plan generated successfully');
      } else {
        console.warn('⚠️ Invalid meal plan structure, using fallback');
        throw new Error('Invalid meal plan structure');
      }
    } catch (error) {
      console.error('❌ Error generating meal plan:', error);
      // Fallback to mock data
      const mockPlan: MealPlan[] = [
        {
          day: 'Monday',
          date: 'Dec 16',
          meals: {
            breakfast: { name: 'Overnight Oats with Berries', calories: 320, time: '8:00 AM' },
            lunch: { name: 'Quinoa Buddha Bowl', calories: 450, time: '12:30 PM' },
            dinner: { name: 'Grilled Salmon & Vegetables', calories: 380, time: '7:00 PM' },
            snack: { name: 'Greek Yogurt with Nuts', calories: 150, time: '3:00 PM' }
          },
          totalCalories: 1300,
          waterGoal: 8,
          completed: true
        },
        {
          day: 'Tuesday',
          date: 'Dec 17',
          meals: {
            breakfast: { name: 'Avocado Toast with Eggs', calories: 380, time: '8:00 AM' },
            lunch: { name: 'Mediterranean Wrap', calories: 420, time: '12:30 PM' },
            dinner: { name: 'Chicken Stir Fry', calories: 400, time: '7:00 PM' }
          },
          totalCalories: 1200,
          waterGoal: 8,
          completed: false
        },
        {
          day: 'Wednesday',
          date: 'Dec 18',
          meals: {
            breakfast: { name: 'Smoothie Bowl', calories: 290, time: '8:00 AM' },
            lunch: { name: 'Lentil Soup & Salad', calories: 380, time: '12:30 PM' },
            dinner: { name: 'Baked Cod with Sweet Potato', calories: 360, time: '7:00 PM' }
          },
          totalCalories: 1030,
          waterGoal: 8,
          completed: false
        }
      ];
      setWeeklyPlan(mockPlan);
    }
    setLoading(false);
  };

  const SimpleMealCard: React.FC<{
    meal: { name: string; calories: number; time: string };
    type: string;
    icon: string;
  }> = ({ meal, type, icon }) => (
    <View style={styles.mealCard}>
      <View style={styles.mealHeader}>
        <View style={styles.mealIcon}>
          <Ionicons name={icon as any} size={16} color={Colors.brand} />
        </View>
        <View style={styles.mealInfo}>
          <Text style={styles.mealType}>{type}</Text>
          <Text style={styles.mealTime}>{meal.time}</Text>
        </View>
        <Text style={styles.mealCalories}>{meal.calories} cal</Text>
      </View>
      <Text style={styles.mealName}>{meal.name}</Text>
    </View>
  );

  const DayCard: React.FC<{ plan: MealPlan; delay: number }> = ({ plan, delay }) => (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
      <TouchableOpacity 
        style={[styles.dayCard, plan.completed && styles.dayCardCompleted]}
        onPress={() => setSelectedDay(selectedDay === plan.day ? null : plan.day)}
      >
        <View style={styles.dayHeader}>
          <View style={styles.dayInfo}>
            <Text style={styles.dayName}>{plan.day}</Text>
            <Text style={styles.dayDate}>{plan.date}</Text>
          </View>
          <View style={styles.dayStats}>
            <View style={styles.caloriesBadge}>
              <Text style={styles.caloriesText}>{plan.totalCalories} cal</Text>
            </View>
            {plan.completed && (
              <View style={styles.completedBadge}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              </View>
            )}
          </View>
        </View>

        {selectedDay === plan.day && (
          <Animated.View entering={FadeInDown.duration(400)} style={styles.dayDetails}>
            <SimpleMealCard meal={plan.meals.breakfast} type="Breakfast" icon="sunny" />
            <SimpleMealCard meal={plan.meals.lunch} type="Lunch" icon="restaurant" />
            <SimpleMealCard meal={plan.meals.dinner} type="Dinner" icon="moon" />
            {plan.meals.snack && (
              <SimpleMealCard meal={plan.meals.snack} type="Snack" icon="cafe" />
            )}
            
            <View style={styles.waterTracker}>
              <View style={styles.waterHeader}>
                <Ionicons name="water" size={16} color={Colors.info} />
                <Text style={styles.waterText}>Water Goal: {plan.waterGoal} glasses</Text>
              </View>
              <View style={styles.waterProgress}>
                {Array.from({ length: plan.waterGoal }, (_, i) => (
                  <View 
                    key={i} 
                    style={[
                      styles.waterGlass,
                      i < (plan.completed ? plan.waterGoal : Math.floor(plan.waterGoal * 0.6)) && styles.waterGlassFilled
                    ]} 
                  />
                ))}
              </View>
            </View>
          </Animated.View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );

  const QuickStats: React.FC = () => (
    <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.statsCard}>
      <Text style={styles.statsTitle}>This Week</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="flame" size={20} color={Colors.warning} />
          </View>
          <Text style={styles.statValue}>9,450</Text>
          <Text style={styles.statLabel}>Calories</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
          </View>
          <Text style={styles.statValue}>5/7</Text>
          <Text style={styles.statLabel}>Days Complete</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="water" size={20} color={Colors.info} />
          </View>
          <Text style={styles.statValue}>42/56</Text>
          <Text style={styles.statLabel}>Glasses</Text>
        </View>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <Ionicons name="trending-up" size={20} color={Colors.brand} />
          </View>
          <Text style={styles.statValue}>85%</Text>
          <Text style={styles.statLabel}>On Track</Text>
        </View>
      </View>
    </Animated.View>
  );

  const getCurrentDayPlan = (): MealPlan => {
    const today = selectedDate.toDateString();
    return weeklyPlan.find(plan => plan.date === today) || {
      day: selectedDate.toLocaleDateString('en', { weekday: 'long' }),
      date: today,
      meals: {
        breakfast: { name: '', calories: 0, time: '8:00 AM' },
        lunch: { name: '', calories: 0, time: '12:30 PM' },
        dinner: { name: '', calories: 0, time: '7:00 PM' },
      },
      totalCalories: 0,
      waterGoal: 8,
      completed: false,
    };
  };

  const handleMealPress = (mealType: string, meal: any) => {
    setSelectedMealType(mealType);
    setShowAddMeal(true);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      {/* Header */}
      <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <Text style={styles.title}>Meal Planner</Text>
            <Text style={styles.subtitle}>Plan your perfect week</Text>
          </View>

          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="notifications" size={24} color={Colors.brand} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="settings" size={24} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>

      {/* View Mode Toggle */}
      <Animated.View entering={FadeInLeft.delay(200).duration(600)} style={styles.viewModeSection}>
        <View style={styles.viewModeToggle}>
          {(['week', 'day', 'timeline'] as const).map((mode) => (
            <TouchableOpacity
              key={mode}
              style={[
                styles.viewModeButton,
                viewMode === mode && styles.viewModeButtonActive
              ]}
              onPress={() => setViewMode(mode)}
            >
              <Ionicons
                name={
                  mode === 'week' ? 'calendar' :
                  mode === 'day' ? 'today' : 'list'
                }
                size={20}
                color={viewMode === mode ? Colors.brandForeground : Colors.mutedForeground}
              />
              <Text style={[
                styles.viewModeText,
                viewMode === mode && styles.viewModeTextActive
              ]}>
                {mode.charAt(0).toUpperCase() + mode.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>

      {/* Week Calendar */}
      <WeekView
        weeklyPlan={weeklyPlan}
        selectedDate={selectedDate}
        onDateSelect={setSelectedDate}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Current Day Overview */}
        <Animated.View entering={FadeInUp.delay(300).duration(600)} style={styles.dayOverviewSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {selectedDate.toLocaleDateString('en', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
            <TouchableOpacity style={styles.addButton} onPress={() => setShowAddMeal(true)}>
              <Ionicons name="add" size={20} color={Colors.brandForeground} />
            </TouchableOpacity>
          </View>

          {viewMode === 'timeline' ? (
            <TimelineView
              dayPlan={getCurrentDayPlan()}
              onMealPress={handleMealPress}
            />
          ) : (
            <View style={styles.dayPlanGrid}>
              {getCurrentDayPlan().meals && Object.entries(getCurrentDayPlan().meals).map(([mealType, meal], index) => {
                if (!meal || (mealType === 'snack' && !meal.name)) return null;

                return (
                  <MealCard
                    key={mealType}
                    meal={meal}
                    mealType={mealType as any}
                    onPress={() => handleMealPress(mealType, meal)}
                    onEdit={() => {}}
                    onDelete={() => {}}
                    index={index}
                  />
                );
              })}
            </View>
          )}
        </Animated.View>

        {/* Nutrition Summary */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.nutritionSection}>
          <Text style={styles.sectionTitle}>Nutrition Summary</Text>
          <ModernCard variant="glass" title="" style={styles.nutritionCard}>
            <View style={styles.nutritionGrid}>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{getCurrentDayPlan().totalCalories}</Text>
                <Text style={styles.nutritionLabel}>Calories</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>85g</Text>
                <Text style={styles.nutritionLabel}>Protein</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>120g</Text>
                <Text style={styles.nutritionLabel}>Carbs</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>45g</Text>
                <Text style={styles.nutritionLabel}>Fat</Text>
              </View>
            </View>
          </ModernCard>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(500).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <ModernButton
              title="Generate Plan"
              onPress={generateWeeklyPlan}
              variant="primary"
              size="md"
              icon="sparkles"
              loading={loading}
              style={styles.quickActionButton}
            />
            <ModernButton
              title="Add Recipe"
              onPress={() => setShowAddMeal(true)}
              variant="secondary"
              size="md"
              icon="restaurant"
              style={styles.quickActionButton}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Add Meal Modal */}
      <ModernModal
        visible={showAddMeal}
        onClose={() => setShowAddMeal(false)}
        title={`Add ${selectedMealType.charAt(0).toUpperCase() + selectedMealType.slice(1)}`}
        variant="center"
        size="lg"
      >
        <View style={styles.addMealContent}>
          <Text style={styles.modalLabel}>Meal Name</Text>
          <ModernInput
            value=""
            onChangeText={() => {}}
            placeholder="Enter meal name..."
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Calories</Text>
          <ModernInput
            value=""
            onChangeText={() => {}}
            placeholder="Enter calories..."
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Time</Text>
          <ModernInput
            value=""
            onChangeText={() => {}}
            placeholder="Enter time..."
            variant="filled"
            style={styles.modalInput}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Cancel"
              onPress={() => setShowAddMeal(false)}
              variant="outline"
              size="md"
              style={styles.modalCancelButton}
            />
            <ModernButton
              title="Add Meal"
              onPress={() => setShowAddMeal(false)}
              variant="primary"
              size="md"
              icon="checkmark"
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Space for tab bar
  },

  // Header
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
  },
  headerActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // View Mode Toggle
  viewModeSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
  },
  viewModeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.md,
    gap: Spacing.xs,
  },
  viewModeButtonActive: {
    backgroundColor: Colors.brand,
  },
  viewModeText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
  },
  viewModeTextActive: {
    color: Colors.brandForeground,
  },

  // Week View
  weekView: {
    marginBottom: Spacing.xl,
  },
  weekViewContainer: {
    paddingHorizontal: Spacing.xl,
    gap: Spacing.md,
  },
  calendarDay: {
    width: 60,
    height: 80,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.card,
    ...Shadows.sm,
  },
  calendarDayButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.md,
  },
  calendarDayText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  calendarDayTextSelected: {
    color: Colors.brandForeground,
  },
  calendarDayTextToday: {
    color: Colors.brand,
  },
  calendarDayName: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
  },
  calendarDayNameSelected: {
    color: Colors.brandForeground,
  },
  planIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.brand,
    marginTop: Spacing.xs,
  },
  planIndicatorSelected: {
    backgroundColor: Colors.brandForeground,
  },

  // Section Styles
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.sm,
  },

  // Day Overview
  dayOverviewSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xxl,
  },
  dayPlanGrid: {
    gap: Spacing.md,
  },

  // Stats Card
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  statValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Generate Button
  generateContainer: {
    marginBottom: Spacing.xl,
  },
  generateButton: {
    backgroundColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    opacity: 0.6,
  },
  generateButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Day Cards
  dayCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dayCardCompleted: {
    borderColor: Colors.success,
    backgroundColor: Colors.brandMuted,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  dayDate: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  dayStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  caloriesBadge: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  caloriesText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  completedBadge: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Day Details
  dayDetails: {
    marginTop: Spacing.lg,
    paddingTop: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },

  // Meal Cards
  mealCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    marginBottom: Spacing.md,
    ...Shadows.sm,
  },
  mealCardButton: {
    padding: Spacing.lg,
  },
  mealCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },
  mealActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mealContent: {
    gap: Spacing.sm,
  },
  mealStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  statText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginLeft: Spacing.xs,
  },

  // Timeline View
  timelineView: {
    paddingHorizontal: Spacing.xl,
  },
  timelineTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xl,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: Spacing.xl,
  },
  timelineLine: {
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.brand,
    marginBottom: Spacing.sm,
  },
  timelineConnector: {
    width: 2,
    flex: 1,
    backgroundColor: Colors.border,
  },
  timelineContent: {
    flex: 1,
  },
  emptyMealSlot: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: 'dashed',
  },
  addMealButton: {
    padding: Spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.md,
  },
  addMealText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Nutrition Section
  nutritionSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xxl,
  },
  nutritionCard: {
    padding: Spacing.lg,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.lg,
  },
  nutritionItem: {
    flex: 1,
    minWidth: '40%',
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brand,
    marginBottom: Spacing.xs,
  },
  nutritionLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },

  // Quick Actions Section
  quickActionsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xxl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },

  // Modal Styles
  addMealContent: {
    gap: Spacing.lg,
  },
  modalLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modalInput: {
    marginBottom: Spacing.md,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  modalCancelButton: {
    flex: 1,
  },
  modalSaveButton: {
    flex: 1,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  mealIcon: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  mealInfo: {
    flex: 1,
  },
  mealType: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  mealTime: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  mealCalories: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  mealName: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    marginLeft: 32,
  },

  // Water Tracker
  waterTracker: {
    marginTop: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.md,
  },
  waterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  waterText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },
  waterProgress: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  waterGlass: {
    width: 16,
    height: 16,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.muted,
  },
  waterGlassFilled: {
    backgroundColor: Colors.info,
  },

  // Tips Section
  tipsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  tipText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 20,
  },

  // Custom Options
  customToggleSection: {
    marginBottom: Spacing.lg,
  },
  customToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    gap: Spacing.sm,
  },
  customToggleText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  customOptionsSection: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  customSectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Custom Prompt
  customPromptSection: {
    marginBottom: Spacing.lg,
  },
  customPromptContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
  },
  customPromptInput: {
    flex: 1,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  voiceButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  voiceButtonActive: {
    backgroundColor: Colors.error,
  },
  voiceTextContainer: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.md,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Tags
  tagsSection: {
    marginBottom: Spacing.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    gap: Spacing.xs,
  },
  tagChipSelected: {
    backgroundColor: Colors.brand,
  },
  tagChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  tagChipTextSelected: {
    color: Colors.brandForeground,
  },

  // Clear Button
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.xs,
  },
  clearButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default PlanScreenModern;
