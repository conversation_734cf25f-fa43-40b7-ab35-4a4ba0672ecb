import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import { userProfile } from '../constants/UserData';

const ProfileScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.backgroundSecondary]}
        style={styles.backgroundGradient}
      >
        <ScrollView style={styles.scrollView}>
          <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
            <View style={styles.avatarContainer}>
              <Ionicons name="person" size={48} color={Colors.primary} />
            </View>
            <Text style={styles.name}>{userProfile.name}</Text>
            <TouchableOpacity style={styles.editButton}>
              <Ionicons name="create-outline" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.profileSection}>
            <Text style={styles.sectionTitle}>Profile Information</Text>

            <View style={styles.profileItem}>
              <View style={styles.profileItemIcon}>
                <Ionicons name="heart" size={20} color={Colors.primary} />
              </View>
              <View style={styles.profileItemContent}>
                <Text style={styles.profileItemLabel}>Goal</Text>
                <Text style={styles.profileItemValue}>{userProfile.dietaryGoal}</Text>
              </View>
            </View>

            <View style={styles.profileItem}>
              <View style={styles.profileItemIcon}>
                <Ionicons name="restaurant" size={20} color={Colors.primary} />
              </View>
              <View style={styles.profileItemContent}>
                <Text style={styles.profileItemLabel}>Diet Type</Text>
                <Text style={styles.profileItemValue}>{userProfile.dietaryType}</Text>
              </View>
            </View>

            <View style={styles.profileItem}>
              <View style={styles.profileItemIcon}>
                <Ionicons name="warning" size={20} color={Colors.warning} />
              </View>
              <View style={styles.profileItemContent}>
                <Text style={styles.profileItemLabel}>Allergies</Text>
                <Text style={styles.profileItemValue}>{userProfile.allergies.join(', ')}</Text>
              </View>
            </View>

            <View style={styles.profileItem}>
              <View style={styles.profileItemIcon}>
                <Ionicons name="close-circle" size={20} color={Colors.error} />
              </View>
              <View style={styles.profileItemContent}>
                <Text style={styles.profileItemLabel}>Dislikes</Text>
                <Text style={styles.profileItemValue}>{userProfile.dislikedIngredients.join(', ')}</Text>
              </View>
            </View>

            <View style={styles.profileItem}>
              <View style={styles.profileItemIcon}>
                <Ionicons name="globe" size={20} color={Colors.secondary} />
              </View>
              <View style={styles.profileItemContent}>
                <Text style={styles.profileItemLabel}>Preferred Cuisines</Text>
                <Text style={styles.profileItemValue}>{userProfile.preferredCuisines.join(', ')}</Text>
              </View>
            </View>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionButton}>
              <LinearGradient
                colors={[Colors.primary, Colors.secondary]}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="settings" size={24} color={Colors.white} />
                <Text style={styles.actionButtonText}>Settings</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <LinearGradient
                colors={[Colors.secondary, Colors.primary]}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="help-circle" size={24} color={Colors.white} />
                <Text style={styles.actionButtonText}>Help & Support</Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    alignItems: 'center',
    position: 'relative',
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primaryOpacity10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  name: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
  },
  editButton: {
    position: 'absolute',
    top: Spacing.xl,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  profileSection: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  profileItemIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.gray50,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  profileItemContent: {
    flex: 1,
  },
  profileItemLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textLight,
    marginBottom: Spacing.xs,
  },
  profileItemValue: {
    fontSize: FontSizes.base,
    color: Colors.text,
    fontWeight: FontWeights.medium,
  },
  actionsContainer: {
    marginBottom: Spacing.xxl,
  },
  actionButton: {
    marginBottom: Spacing.md,
  },
  actionButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  actionButtonText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
});

export default ProfileScreen;
