import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import { userProfile } from '../constants/UserData';

const ProfileScreenModern: React.FC = () => {
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [mealReminders, setMealReminders] = useState(true);
  const [waterReminders, setWaterReminders] = useState(true);

  const ProfileHeader: React.FC = () => (
    <Animated.View entering={FadeInDown.duration(600)} style={styles.profileHeader}>
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {userProfile.name.split(' ').map(n => n[0]).join('')}
          </Text>
        </View>
        <View style={styles.statusBadge}>
          <Ionicons name="checkmark" size={12} color={Colors.brandForeground} />
        </View>
      </View>
      <Text style={styles.userName}>{userProfile.name}</Text>
      <Text style={styles.userEmail}><EMAIL></Text>
      <View style={styles.membershipBadge}>
        <Ionicons name="star" size={14} color={Colors.warning} />
        <Text style={styles.membershipText}>Premium Member</Text>
      </View>
    </Animated.View>
  );

  const StatsCard: React.FC = () => (
    <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Progress</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>28</Text>
          <Text style={styles.statLabel}>Days Active</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>156</Text>
          <Text style={styles.statLabel}>Meals Logged</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>89%</Text>
          <Text style={styles.statLabel}>Goal Achievement</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>12</Text>
          <Text style={styles.statLabel}>Recipes Saved</Text>
        </View>
      </View>
    </Animated.View>
  );

  const SettingItem: React.FC<{
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
    showChevron?: boolean;
  }> = ({ icon, title, subtitle, onPress, rightElement, showChevron = true }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.brand} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightElement || (showChevron && (
        <Ionicons name="chevron-forward" size={16} color={Colors.mutedForeground} />
      ))}
    </TouchableOpacity>
  );

  const SettingsSection: React.FC<{ title: string; children: React.ReactNode; delay: number }> = ({ 
    title, 
    children, 
    delay 
  }) => (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)} style={styles.settingsSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.settingsCard}>
        {children}
      </View>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <ProfileHeader />

        {/* Stats Card */}
        <StatsCard />

        {/* Health Goals */}
        <SettingsSection title="Health Goals" delay={400}>
          <SettingItem
            icon="target"
            title="Daily Calorie Goal"
            subtitle="2,000 calories"
            onPress={() => {}}
          />
          <SettingItem
            icon="water"
            title="Water Intake Goal"
            subtitle="8 glasses per day"
            onPress={() => {}}
          />
          <SettingItem
            icon="fitness"
            title="Activity Level"
            subtitle="Moderately Active"
            onPress={() => {}}
          />
          <SettingItem
            icon="scale"
            title="Weight Goal"
            subtitle="Maintain current weight"
            onPress={() => {}}
            showChevron={false}
          />
        </SettingsSection>

        {/* Preferences */}
        <SettingsSection title="Preferences" delay={600}>
          <SettingItem
            icon="restaurant"
            title="Dietary Restrictions"
            subtitle="Vegetarian, Gluten-free"
            onPress={() => {}}
          />
          <SettingItem
            icon="leaf"
            title="Food Allergies"
            subtitle="Nuts, Shellfish"
            onPress={() => {}}
          />
          <SettingItem
            icon="time"
            title="Meal Times"
            subtitle="Breakfast 8AM, Lunch 1PM, Dinner 7PM"
            onPress={() => {}}
          />
        </SettingsSection>

        {/* Notifications */}
        <SettingsSection title="Notifications" delay={800}>
          <SettingItem
            icon="notifications"
            title="Push Notifications"
            subtitle="Get reminders and updates"
            rightElement={
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: Colors.muted, true: Colors.brand }}
                thumbColor={Colors.brandForeground}
              />
            }
            showChevron={false}
          />
          <SettingItem
            icon="restaurant"
            title="Meal Reminders"
            subtitle="Remind me to log meals"
            rightElement={
              <Switch
                value={mealReminders}
                onValueChange={setMealReminders}
                trackColor={{ false: Colors.muted, true: Colors.brand }}
                thumbColor={Colors.brandForeground}
              />
            }
            showChevron={false}
          />
          <SettingItem
            icon="water"
            title="Water Reminders"
            subtitle="Stay hydrated throughout the day"
            rightElement={
              <Switch
                value={waterReminders}
                onValueChange={setWaterReminders}
                trackColor={{ false: Colors.muted, true: Colors.brand }}
                thumbColor={Colors.brandForeground}
              />
            }
            showChevron={false}
          />
        </SettingsSection>

        {/* App Settings */}
        <SettingsSection title="App Settings" delay={1000}>
          <SettingItem
            icon="moon"
            title="Dark Mode"
            subtitle="Switch to dark theme"
            rightElement={
              <Switch
                value={darkMode}
                onValueChange={setDarkMode}
                trackColor={{ false: Colors.muted, true: Colors.brand }}
                thumbColor={Colors.brandForeground}
              />
            }
            showChevron={false}
          />
          <SettingItem
            icon="language"
            title="Language"
            subtitle="English"
            onPress={() => {}}
          />
          <SettingItem
            icon="download"
            title="Data Export"
            subtitle="Download your nutrition data"
            onPress={() => {}}
          />
        </SettingsSection>

        {/* Support */}
        <SettingsSection title="Support" delay={1200}>
          <SettingItem
            icon="help-circle"
            title="Help Center"
            subtitle="FAQs and guides"
            onPress={() => {}}
          />
          <SettingItem
            icon="chatbubble"
            title="Contact Support"
            subtitle="Get help from our team"
            onPress={() => {}}
          />
          <SettingItem
            icon="star"
            title="Rate App"
            subtitle="Share your feedback"
            onPress={() => {}}
          />
          <SettingItem
            icon="document-text"
            title="Privacy Policy"
            onPress={() => {}}
          />
          <SettingItem
            icon="shield-checkmark"
            title="Terms of Service"
            onPress={() => {}}
            showChevron={false}
          />
        </SettingsSection>

        {/* Sign Out */}
        <Animated.View entering={FadeInUp.delay(1400).duration(600)} style={styles.signOutSection}>
          <TouchableOpacity style={styles.signOutButton}>
            <Ionicons name="log-out" size={20} color={Colors.error} />
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* App Version */}
        <Animated.View entering={FadeInUp.delay(1600).duration(600)} style={styles.versionSection}>
          <Text style={styles.versionText}>Nutri AI v1.0.0</Text>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },

  // Profile Header
  profileHeader: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: Spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  userName: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    gap: Spacing.xs,
  },
  membershipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Stats Card
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statValue: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },

  // Settings Sections
  settingsSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  settingsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  settingSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Sign Out Section
  signOutSection: {
    marginBottom: Spacing.xl,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.lg,
    gap: Spacing.sm,
  },
  signOutText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.error,
  },

  // Version Section
  versionSection: {
    alignItems: 'center',
    paddingBottom: Spacing.xl,
  },
  versionText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
});

export default ProfileScreenModern;
