import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Dimensions,
  StatusBar,
  ImageBackground,
  Alert,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import { ModernInput } from '../components/ModernInput';
import { userProfile } from '../constants/UserData';

const { width, height } = Dimensions.get('window');

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  progress?: number;
  index: number;
}

interface AchievementBadgeProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  earned: boolean;
  progress?: number;
  index: number;
}

interface SettingItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
  variant?: 'default' | 'danger' | 'premium';
}

// Modern Stat Card Component
const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  progress = 0,
  index,
}) => {
  const scale = useSharedValue(1);
  const progressWidth = useSharedValue(0);

  useEffect(() => {
    progressWidth.value = withDelay(index * 200, withTiming(progress, { duration: 1000 }));
  }, [progress, index]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  };

  return (
    <Animated.View
      entering={ZoomIn.delay(index * 100).duration(600)}
      style={[styles.statCard, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.statCardButton}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.statCardHeader}>
          <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon} size={24} color={color} />
          </View>
          <Text style={styles.statTitle}>{title}</Text>
        </View>

        <Text style={[styles.statValue, { color }]}>{value}</Text>
        <Text style={styles.statSubtitle}>{subtitle}</Text>

        {progress > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, { backgroundColor: color }, progressAnimatedStyle]} />
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Achievement Badge Component
const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  title,
  description,
  icon,
  earned,
  progress = 0,
  index,
}) => {
  const scale = useSharedValue(earned ? 1 : 0.95);
  const opacity = useSharedValue(earned ? 1 : 0.6);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      entering={SlideInUp.delay(index * 150).duration(600)}
      style={[styles.achievementBadge, animatedStyle]}
    >
      <View style={[styles.achievementIcon, earned && styles.achievementIconEarned]}>
        <Ionicons
          name={icon}
          size={28}
          color={earned ? Colors.warning : Colors.mutedForeground}
        />
      </View>
      <View style={styles.achievementContent}>
        <Text style={[styles.achievementTitle, earned && styles.achievementTitleEarned]}>
          {title}
        </Text>
        <Text style={styles.achievementDescription}>{description}</Text>
        {!earned && progress > 0 && (
          <View style={styles.achievementProgress}>
            <View style={styles.achievementProgressBar}>
              <View style={[styles.achievementProgressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.achievementProgressText}>{Math.round(progress)}%</Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

// Modern Profile Header Component
const ProfileHeader: React.FC<{ onEditProfile: () => void; onShowAchievements: () => void }> = ({ onEditProfile, onShowAchievements }) => (
    <Animated.View entering={FadeInDown.duration(800)} style={styles.profileHeader}>
      <LinearGradient
        colors={[Colors.brand, Colors.brandSecondary, Colors.brandTertiary]}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="settings" size={24} color={Colors.brandForeground} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={() => Share.share({ message: 'Check out this amazing nutrition app!' })}>
              <Ionicons name="share" size={24} color={Colors.brandForeground} />
            </TouchableOpacity>
          </View>

          <Animated.View entering={ZoomIn.delay(300).duration(600)} style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <LinearGradient
                colors={[Colors.brandForeground, Colors.background]}
                style={styles.avatarGradient}
              >
                <Text style={styles.avatarText}>
                  {userProfile.name.split(' ').map(n => n[0]).join('')}
                </Text>
              </LinearGradient>
            </View>
            <View style={styles.statusBadge}>
              <Ionicons name="checkmark" size={14} color={Colors.brandForeground} />
            </View>
            <TouchableOpacity style={styles.editAvatarButton}>
              <Ionicons name="camera" size={16} color={Colors.brandForeground} />
            </TouchableOpacity>
          </Animated.View>

          <Animated.View entering={SlideInUp.delay(500).duration(600)} style={styles.userInfo}>
            <Text style={styles.userName}>{userProfile.name}</Text>
            <Text style={styles.userEmail}><EMAIL></Text>

            <View style={styles.membershipContainer}>
              <LinearGradient
                colors={[Colors.warning, Colors.warning + '80']}
                style={styles.membershipBadge}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Ionicons name="star" size={16} color={Colors.brandForeground} />
                <Text style={styles.membershipText}>Premium Member</Text>
              </LinearGradient>
            </View>

            <View style={styles.profileActions}>
              <ModernButton
                title="Edit Profile"
                onPress={onEditProfile}
                variant="glass"
                size="sm"
                icon="pencil"
                style={styles.editButton}
              />
              <ModernButton
                title="Achievements"
                onPress={onShowAchievements}
                variant="glass"
                size="sm"
                icon="trophy"
                style={styles.achievementsButton}
              />
            </View>
          </Animated.View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

// Stats Card Component
const StatsCard: React.FC = () => (
    <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Progress</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>28</Text>
          <Text style={styles.statLabel}>Days Active</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>156</Text>
          <Text style={styles.statLabel}>Meals Logged</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>89%</Text>
          <Text style={styles.statLabel}>Goal Achievement</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>12</Text>
          <Text style={styles.statLabel}>Recipes Saved</Text>
        </View>
      </View>
    </Animated.View>
  );

// Settings Section Component
const SettingsSection: React.FC<{ title: string; children: React.ReactNode; delay: number }> = ({
  title,
  children,
  delay
}) => (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)} style={styles.settingsSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.settingsCard}>
        {children}
      </View>
    </Animated.View>
  );

  const statsData = [
    {
      title: 'Streak',
      value: '12',
      subtitle: 'days in a row',
      icon: 'flame' as const,
      color: Colors.warning,
      progress: 85,
    },
    {
      title: 'Weight',
      value: '68.5',
      subtitle: 'kg current',
      icon: 'fitness' as const,
      color: Colors.success,
      progress: 70,
    },
    {
      title: 'Calories',
      value: '1,847',
      subtitle: 'avg daily',
      icon: 'nutrition' as const,
      color: Colors.brand,
      progress: 92,
    },
    {
      title: 'Water',
      value: '2.1',
      subtitle: 'liters today',
      icon: 'water' as const,
      color: Colors.info,
      progress: 75,
    },
  ];

const ProfileScreenModern: React.FC = () => {
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [mealReminders, setMealReminders] = useState(true);
  const [waterReminders, setWaterReminders] = useState(true);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [showAchievements, setShowAchievements] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const achievements = [
    {
      id: '1',
      title: 'First Scan',
      description: 'Scanned your first food item',
      icon: 'camera' as const,
      earned: true,
      progress: 100,
    },
    {
      id: '2',
      title: 'Week Warrior',
      description: 'Tracked meals for 7 days straight',
      icon: 'calendar' as const,
      earned: true,
      progress: 100,
    },
    {
      id: '3',
      title: 'Nutrition Expert',
      description: 'Asked 50 nutrition questions',
      icon: 'school' as const,
      earned: false,
      progress: 76,
    },
    {
      id: '4',
      title: 'Recipe Master',
      description: 'Saved 25 recipes',
      icon: 'book' as const,
      earned: false,
      progress: 60,
    },
    {
      id: '5',
      title: 'Goal Crusher',
      description: 'Achieved your monthly goal',
      icon: 'trophy' as const,
      earned: false,
      progress: 40,
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Billion-Dollar Profile Header */}
      <ProfileHeader
        onEditProfile={() => setShowEditProfile(true)}
        onShowAchievements={() => setShowAchievements(true)}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Stats Grid */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          <View style={styles.statsGrid}>
            {statsData.map((stat, index) => (
              <StatCard
                key={stat.title}
                title={stat.title}
                value={stat.value}
                subtitle={stat.subtitle}
                icon={stat.icon}
                color={stat.color}
                progress={stat.progress}
                index={index}
              />
            ))}
          </View>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(300).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <ModernButton
              title="View Reports"
              onPress={() => {}}
              variant="primary"
              size="md"
              icon="analytics"
              style={styles.quickActionButton}
            />
            <ModernButton
              title="Export Data"
              onPress={() => {}}
              variant="secondary"
              size="md"
              icon="download"
              style={styles.quickActionButton}
            />
          </View>
        </Animated.View>

        {/* Settings Sections */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Settings</Text>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="flag"
              title="Daily Calorie Goal"
              subtitle="2,000 calories"
              onPress={() => {}}
            />
            <SettingItem
              icon="water"
              title="Water Intake Goal"
              subtitle="8 glasses per day"
              onPress={() => {}}
            />
            <SettingItem
              icon="fitness"
              title="Activity Level"
              subtitle="Moderately Active"
              onPress={() => {}}
            />
            <SettingItem
              icon="scale"
              title="Weight Goal"
              subtitle="Maintain current weight"
              onPress={() => {}}
              showChevron={false}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="restaurant"
              title="Dietary Restrictions"
              subtitle="Vegetarian, Gluten-free"
              onPress={() => {}}
            />
            <SettingItem
              icon="leaf"
              title="Food Allergies"
              subtitle="Nuts, Shellfish"
              onPress={() => {}}
            />
            <SettingItem
              icon="time"
              title="Meal Times"
              subtitle="Breakfast 8AM, Lunch 1PM, Dinner 7PM"
              onPress={() => {}}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="notifications"
              title="Push Notifications"
              subtitle="Get reminders and updates"
              rightElement={
                <Switch
                  value={notifications}
                  onValueChange={setNotifications}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={notifications ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="restaurant"
              title="Meal Reminders"
              subtitle="Remind me to log meals"
              rightElement={
                <Switch
                  value={mealReminders}
                  onValueChange={setMealReminders}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={mealReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="water"
              title="Water Reminders"
              subtitle="Stay hydrated throughout the day"
              rightElement={
                <Switch
                  value={waterReminders}
                  onValueChange={setWaterReminders}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={waterReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="help-circle"
              title="Help & Support"
              subtitle="Get help and contact support"
              onPress={() => {}}
            />
            <SettingItem
              icon="shield-checkmark"
              title="Privacy Policy"
              subtitle="Read our privacy policy"
              onPress={() => {}}
            />
            <SettingItem
              icon="document-text"
              title="Terms of Service"
              subtitle="View terms and conditions"
              onPress={() => {}}
            />
            <SettingItem
              icon="log-out"
              title="Sign Out"
              subtitle="Sign out of your account"
              onPress={() => Alert.alert('Sign Out', 'Are you sure you want to sign out?')}
              variant="danger"
            />
          </ModernCard>
        </Animated.View>

        {/* App Version */}
        <Animated.View entering={FadeInUp.delay(1600).duration(600)} style={styles.versionSection}>
          <Text style={styles.versionText}>Nutri AI v1.0.0</Text>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Edit Profile Modal */}
      <ModernModal
        visible={showEditProfile}
        onClose={() => setShowEditProfile(false)}
        title="Edit Profile"
        variant="center"
        size="lg"
      >
        <View style={styles.editProfileContent}>
          <Text style={styles.modalLabel}>Name</Text>
          <ModernInput
            value={userProfile.name}
            onChangeText={() => {}}
            placeholder="Enter your name"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Email</Text>
          <ModernInput
            value="<EMAIL>"
            onChangeText={() => {}}
            placeholder="Enter your email"
            keyboardType="email-address"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Height (cm)</Text>
          <ModernInput
            value="165"
            onChangeText={() => {}}
            placeholder="Enter your height"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Weight (kg)</Text>
          <ModernInput
            value="68.5"
            onChangeText={() => {}}
            placeholder="Enter your weight"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Cancel"
              onPress={() => setShowEditProfile(false)}
              variant="outline"
              size="md"
              style={styles.modalCancelButton}
            />
            <ModernButton
              title="Save Changes"
              onPress={() => setShowEditProfile(false)}
              variant="primary"
              size="md"
              icon="checkmark"
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>

      {/* Achievements Modal */}
      <ModernModal
        visible={showAchievements}
        onClose={() => setShowAchievements(false)}
        title="Achievements"
        variant="fullscreen"
      >
        <ScrollView style={styles.achievementsScroll} showsVerticalScrollIndicator={false}>
          <View style={styles.achievementsGrid}>
            {achievements.map((achievement, index) => (
              <AchievementBadge
                key={achievement.title}
                title={achievement.title}
                description={achievement.description}
                icon={achievement.icon}
                earned={achievement.earned}
                progress={achievement.progress}
                index={index}
              />
            ))}
          </View>
        </ScrollView>
      </ModernModal>
    </View>
  );
};

// Setting Item Component
const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  showChevron = true,
  variant = 'default',
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    if (onPress) {
      scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    }
  };

  const handlePressOut = () => {
    if (onPress) {
      scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'danger': return Colors.error;
      case 'premium': return Colors.warning;
      default: return Colors.brand;
    }
  };

  const getTitleColor = () => {
    switch (variant) {
      case 'danger': return Colors.error;
      default: return Colors.foreground;
    }
  };

  return (
    <Animated.View style={[styles.settingItem, animatedStyle]}>
      <TouchableOpacity
        style={styles.settingItemButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
      >
        <View style={styles.settingItemLeft}>
          <View style={[styles.settingIcon, { backgroundColor: getIconColor() + '20' }]}>
            <Ionicons name={icon} size={20} color={getIconColor()} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: getTitleColor() }]}>{title}</Text>
            {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
          </View>
        </View>

        <View style={styles.settingItemRight}>
          {rightElement}
          {showChevron && onPress && (
            <Ionicons name="chevron-forward" size={20} color={Colors.mutedForeground} />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Profile Header
  profileHeader: {
    marginBottom: Spacing.xl,
  },
  headerGradient: {
    paddingTop: 60, // Account for status bar
    paddingBottom: Spacing.xxxl,
    paddingHorizontal: Spacing.xl,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: Spacing.xl,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: Spacing.xl,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    ...Shadows.xl,
  },
  avatarGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  userName: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  userInfo: {
    alignItems: 'center',
  },
  membershipContainer: {
    marginBottom: Spacing.lg,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    gap: Spacing.xs,
  },
  membershipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  profileActions: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  editButton: {
    flex: 1,
  },
  achievementsButton: {
    flex: 1,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },

  // Stats Section
  statsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },

  // Stat Card
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  statCardButton: {
    padding: Spacing.lg,
  },
  statCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  statTitle: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    flex: 1,
  },
  statValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    marginBottom: Spacing.xs,
  },
  statSubtitle: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
  },
  progressContainer: {
    marginTop: Spacing.sm,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.sm,
  },

  // Achievement Badge
  achievementBadge: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  achievementIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  achievementIconEarned: {
    backgroundColor: Colors.warning + '20',
  },
  achievementContent: {
    flex: 1,
    justifyContent: 'center',
  },
  achievementTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  achievementTitleEarned: {
    color: Colors.foreground,
  },
  achievementDescription: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 18,
  },
  achievementProgress: {
    marginTop: Spacing.md,
  },
  achievementProgressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
  },
  achievementProgressFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },
  achievementProgressText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    textAlign: 'right',
  },


  // Legacy Stats Card (for StatsCard component)
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },

  // Quick Actions
  quickActionsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },

  // Settings Container
  settingsContainer: {
    paddingHorizontal: Spacing.xl,
  },

  // Settings Sections
  settingsSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  settingsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    width: '100%',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  settingSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Sign Out Section
  signOutSection: {
    marginBottom: Spacing.xl,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.lg,
    gap: Spacing.sm,
  },
  signOutText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.error,
  },

  // Version Section
  versionSection: {
    alignItems: 'center',
    paddingBottom: Spacing.xl,
  },
  versionText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Modal Styles
  editProfileContent: {
    gap: Spacing.lg,
  },
  modalLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modalInput: {
    marginBottom: Spacing.md,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  modalCancelButton: {
    flex: 1,
  },
  modalSaveButton: {
    flex: 1,
  },
  achievementsScroll: {
    flex: 1,
  },
  achievementsGrid: {
    padding: Spacing.lg,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default ProfileScreenModern;
