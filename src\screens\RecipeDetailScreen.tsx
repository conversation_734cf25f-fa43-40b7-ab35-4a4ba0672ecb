import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernButton } from '../components/ModernButton';

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
}

const RecipeDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const recipe = route.params?.recipe as Recipe;
  const [isFavorited, setIsFavorited] = useState(false);
  const [servings, setServings] = useState(4);

  // Enhanced recipe states
  const [activeTab, setActiveTab] = useState<'ingredients' | 'instructions' | 'nutrition'>('ingredients');
  const [completedSteps, setCompletedSteps] = useState<boolean[]>([]);
  const [cookingMode, setCookingMode] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [timers, setTimers] = useState<{[key: number]: number}>({});
  const [activeTimers, setActiveTimers] = useState<{[key: number]: boolean}>({});
  const [notes, setNotes] = useState('');
  const [difficulty, setDifficulty] = useState(recipe?.difficulty || 'Medium');
  const [prepTime, setPrepTime] = useState(15);
  const [nutritionExpanded, setNutritionExpanded] = useState(false);
  const [ingredientChecked, setIngredientChecked] = useState<boolean[]>([]);
  const [scalingHistory, setScalingHistory] = useState<number[]>([4]);
  const [cookingTips, setCookingTips] = useState<string[]>([]);

  // Enhanced recipe functions
  const adjustServings = (newServings: number) => {
    if (newServings < 1 || newServings > 20) return;
    setScalingHistory(prev => [...prev, newServings]);
    setServings(newServings);
  };

  const scaleIngredient = (ingredient: string): string => {
    const scaleFactor = servings / 4; // Assuming original recipe is for 4 servings

    // Extract numbers and units from ingredient
    const numberRegex = /(\d+(?:\.\d+)?(?:\/\d+)?)/g;
    const matches = ingredient.match(numberRegex);

    if (matches) {
      let scaledIngredient = ingredient;
      matches.forEach(match => {
        const originalValue = parseFloat(match.includes('/') ?
          match.split('/').reduce((a, b) => parseFloat(a) / parseFloat(b)) : match);
        const scaledValue = originalValue * scaleFactor;
        const formattedValue = scaledValue % 1 === 0 ?
          scaledValue.toString() :
          scaledValue.toFixed(scaledValue < 1 ? 2 : 1);
        scaledIngredient = scaledIngredient.replace(match, formattedValue);
      });
      return scaledIngredient;
    }

    return ingredient;
  };

  const calculateNutrition = () => {
    const scaleFactor = servings / 4;
    return {
      calories: Math.round((recipe?.calories || 350) * scaleFactor),
      protein: Math.round(25 * scaleFactor),
      carbs: Math.round(45 * scaleFactor),
      fat: Math.round(12 * scaleFactor),
      fiber: Math.round(8 * scaleFactor),
      sugar: Math.round(6 * scaleFactor),
      sodium: Math.round(680 * scaleFactor),
    };
  };

  const toggleIngredientCheck = (index: number) => {
    const newChecked = [...ingredientChecked];
    newChecked[index] = !newChecked[index];
    setIngredientChecked(newChecked);
  };

  const startCookingMode = () => {
    setCookingMode(true);
    setCurrentStep(0);
    setCompletedSteps(new Array(recipe.instructions.length).fill(false));
    navigation.navigate('CookingTimer', {
      recipe: recipe.title,
      instructions: recipe.instructions,
      servings
    });
  };

  const generateCookingTips = () => {
    const tips = [
      'Read through all instructions before starting',
      'Prep all ingredients first (mise en place)',
      'Preheat your oven if needed',
      'Taste and adjust seasoning as you go',
      'Keep a clean workspace while cooking',
    ];
    setCookingTips(tips.slice(0, 3));
  };

  if (!recipe) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={Colors.error} />
          <Text style={styles.errorText}>Recipe not found</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const shareRecipe = async () => {
    try {
      const recipeText = `${recipe.title}\n\nIngredients:\n${recipe.ingredients.map(ing => `• ${ing}`).join('\n')}\n\nInstructions:\n${recipe.instructions.map((inst, i) => `${i + 1}. ${inst}`).join('\n')}`;
      
      await Share.share({
        message: recipeText,
        title: recipe.title,
      });
    } catch (error) {
      console.error('Error sharing recipe:', error);
    }
  };



  // Calculate adjusted ingredient quantities based on serving size
  const getAdjustedIngredients = () => {
    const originalServings = 4; // Assume recipes are for 4 servings by default
    const multiplier = servings / originalServings;

    return recipe.ingredients.map(ingredient => {
      // Extract numbers from ingredient string and multiply them
      return ingredient.replace(/(\d+(?:\.\d+)?)/g, (match) => {
        const number = parseFloat(match);
        const adjusted = (number * multiplier).toFixed(number % 1 === 0 ? 0 : 1);
        return adjusted;
      });
    });
  };

  // Calculate adjusted calories
  const getAdjustedCalories = () => {
    const originalServings = 4;
    const multiplier = servings / originalServings;
    return Math.round(recipe.calories * multiplier);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return Colors.success;
      case 'Medium': return Colors.warning;
      case 'Hard': return Colors.error;
      default: return Colors.mutedForeground;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.foreground} />
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={() => setIsFavorited(!isFavorited)}
          >
            <Ionicons 
              name={isFavorited ? "heart" : "heart-outline"} 
              size={24} 
              color={isFavorited ? Colors.error : Colors.foreground} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={shareRecipe}
          >
            <Ionicons name="share-outline" size={24} color={Colors.foreground} />
          </TouchableOpacity>
        </View>
      </Animated.View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Recipe Header */}
        <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.recipeHeader}>
          <Text style={styles.recipeTitle}>{recipe.title}</Text>
          <Text style={styles.recipeDescription}>{recipe.description}</Text>
          
          <View style={styles.recipeStats}>
            <View style={styles.statItem}>
              <Ionicons name="time" size={16} color={Colors.mutedForeground} />
              <Text style={styles.statText}>{recipe.cookTime}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="flame" size={16} color={Colors.mutedForeground} />
              <Text style={styles.statText}>{getAdjustedCalories()} cal</Text>
            </View>
            <View style={[styles.statItem, styles.difficultyItem]}>
              <View style={[styles.difficultyDot, { backgroundColor: getDifficultyColor(recipe.difficulty) }]} />
              <Text style={styles.statText}>{recipe.difficulty}</Text>
            </View>
          </View>

          <View style={styles.recipeTags}>
            {recipe.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Modern Servings Section */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.servingsSection}>
          <View style={styles.servingsCard}>
            <View style={styles.servingsHeader}>
              <Text style={styles.sectionTitle}>Servings</Text>
              <View style={styles.nutritionPreview}>
                <Text style={styles.nutritionPreviewText}>
                  {calculateNutrition().calories} cal per serving
                </Text>
              </View>
            </View>

            <View style={styles.servingsAdjuster}>
              <TouchableOpacity
                style={[styles.servingsButton, servings <= 1 && styles.servingsButtonDisabled]}
                onPress={() => adjustServings(servings - 1)}
                disabled={servings <= 1}
                activeOpacity={0.8}
              >
                <Ionicons name="remove" size={18} color={servings <= 1 ? Colors.mutedForeground : Colors.brand} />
              </TouchableOpacity>

              <View style={styles.servingsDisplay}>
                <Text style={styles.servingsText}>{servings}</Text>
                <Text style={styles.servingsLabel}>servings</Text>
              </View>

              <TouchableOpacity
                style={[styles.servingsButton, servings >= 20 && styles.servingsButtonDisabled]}
                onPress={() => adjustServings(servings + 1)}
                disabled={servings >= 20}
                activeOpacity={0.8}
              >
                <Ionicons name="add" size={18} color={servings >= 20 ? Colors.mutedForeground : Colors.brand} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Quick serving presets */}
          <View style={styles.servingPresets}>
            {[2, 4, 6, 8].map(preset => (
              <TouchableOpacity
                key={preset}
                style={[
                  styles.presetButton,
                  servings === preset && styles.presetButtonActive
                ]}
                onPress={() => adjustServings(preset)}
              >
                <Text style={[
                  styles.presetButtonText,
                  servings === preset && styles.presetButtonTextActive
                ]}>{preset}</Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Scaling indicator */}
          {servings !== 4 && (
            <View style={styles.scalingIndicator}>
              <Ionicons name="resize-outline" size={16} color={Colors.brand} />
              <Text style={styles.scalingText}>
                Recipe scaled {servings > 4 ? 'up' : 'down'} by {Math.abs(((servings - 4) / 4 * 100)).toFixed(0)}%
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Enhanced Ingredients */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Ingredients</Text>
            <View style={styles.ingredientStats}>
              <Text style={styles.ingredientStatsText}>
                {ingredientChecked.filter(Boolean).length}/{recipe.ingredients.length} checked
              </Text>
            </View>
          </View>

          <View style={styles.ingredientsCard}>
            {getAdjustedIngredients().map((ingredient, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.ingredientItem,
                  ingredientChecked[index] && styles.ingredientItemChecked
                ]}
                onPress={() => toggleIngredientCheck(index)}
              >
                <View style={[
                  styles.ingredientCheckbox,
                  ingredientChecked[index] && styles.ingredientCheckboxChecked
                ]}>
                  {ingredientChecked[index] && (
                    <Ionicons name="checkmark" size={16} color={Colors.brandForeground} />
                  )}
                </View>
                <Text style={[
                  styles.ingredientText,
                  ingredientChecked[index] && styles.ingredientTextChecked
                ]}>{scaleIngredient(ingredient)}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Instructions */}
        <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Instructions</Text>
          <View style={styles.instructionsCard}>
            {recipe.instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionItem}>
                <View style={styles.instructionNumber}>
                  <Text style={styles.instructionNumberText}>{index + 1}</Text>
                </View>
                <Text style={styles.instructionText}>{instruction}</Text>
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Enhanced Nutrition Info */}
        <Animated.View entering={FadeInUp.delay(1000).duration(800)} style={styles.section}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => setNutritionExpanded(!nutritionExpanded)}
          >
            <Text style={styles.sectionTitle}>Nutrition (per serving)</Text>
            <Ionicons
              name={nutritionExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color={Colors.mutedForeground}
            />
          </TouchableOpacity>

          <View style={styles.nutritionCard}>
            {/* Main macros */}
            <View style={styles.nutritionGrid}>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{calculateNutrition().calories}</Text>
                <Text style={styles.nutritionLabel}>Calories</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{calculateNutrition().protein}g</Text>
                <Text style={styles.nutritionLabel}>Protein</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{calculateNutrition().carbs}g</Text>
                <Text style={styles.nutritionLabel}>Carbs</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{calculateNutrition().fat}g</Text>
                <Text style={styles.nutritionLabel}>Fat</Text>
              </View>
            </View>

            {/* Expanded nutrition details */}
            {nutritionExpanded && (
              <View style={styles.nutritionExpanded}>
                <View style={styles.nutritionRow}>
                  <Text style={styles.nutritionDetailLabel}>Fiber</Text>
                  <Text style={styles.nutritionDetailValue}>{calculateNutrition().fiber}g</Text>
                </View>
                <View style={styles.nutritionRow}>
                  <Text style={styles.nutritionDetailLabel}>Sugar</Text>
                  <Text style={styles.nutritionDetailValue}>{calculateNutrition().sugar}g</Text>
                </View>
                <View style={styles.nutritionRow}>
                  <Text style={styles.nutritionDetailLabel}>Sodium</Text>
                  <Text style={styles.nutritionDetailValue}>{calculateNutrition().sodium}mg</Text>
                </View>

                {/* Nutrition progress bars */}
                <View style={styles.nutritionProgress}>
                  <Text style={styles.nutritionProgressTitle}>Daily Value %</Text>
                  <View style={styles.progressItem}>
                    <Text style={styles.progressLabel}>Protein</Text>
                    <View style={styles.progressBarContainer}>
                      <View style={[styles.progressBar, { width: `${Math.min(calculateNutrition().protein / 50 * 100, 100)}%` }]} />
                    </View>
                    <Text style={styles.progressValue}>{Math.round(calculateNutrition().protein / 50 * 100)}%</Text>
                  </View>
                  <View style={styles.progressItem}>
                    <Text style={styles.progressLabel}>Fiber</Text>
                    <View style={styles.progressBarContainer}>
                      <View style={[styles.progressBar, { width: `${Math.min(calculateNutrition().fiber / 25 * 100, 100)}%` }]} />
                    </View>
                    <Text style={styles.progressValue}>{Math.round(calculateNutrition().fiber / 25 * 100)}%</Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        </Animated.View>

        {/* Ultra Modern Action Section */}
        <Animated.View entering={FadeInUp.delay(1200).duration(800)} style={styles.actionSection}>
          {/* Primary Action - Start Cooking */}
          <ModernButton
            title={`Start Cooking (${servings} servings)`}
            onPress={startCookingMode}
            variant="primary"
            size="xl"
            icon="restaurant"
            fullWidth
          />

          {/* Secondary Actions Grid */}
          <View style={styles.secondaryActionsGrid}>
            <ModernButton
              title="Save Recipe"
              onPress={() => {}}
              variant="outline"
              size="md"
              icon="bookmark"
              style={styles.gridButton}
            />

            <ModernButton
              title="Add to Cart"
              onPress={() => {}}
              variant="outline"
              size="md"
              icon="cart"
              style={styles.gridButton}
            />
          </View>

          <View style={styles.secondaryActionsGrid}>
            <ModernButton
              title="Set Timer"
              onPress={() => {}}
              variant="secondary"
              size="md"
              icon="timer"
              style={styles.gridButton}
            />

            <ModernButton
              title="Share Recipe"
              onPress={() => {}}
              variant="secondary"
              size="md"
              icon="share"
              style={styles.gridButton}
            />
          </View>

          {/* Cooking Tips Card */}
          {cookingTips.length === 0 && (
            <ModernButton
              title="Get Smart Cooking Tips"
              onPress={generateCookingTips}
              variant="ghost"
              size="md"
              icon="bulb"
              iconPosition="left"
              fullWidth
            />
          )}

          {cookingTips.length > 0 && (
            <View style={styles.tipsContainer}>
              <View style={styles.tipsHeader}>
                <View style={styles.tipsHeaderIcon}>
                  <Ionicons name="bulb" size={18} color={Colors.accent} />
                </View>
                <Text style={styles.tipsHeaderTitle}>Smart Tips</Text>
              </View>
              <View style={styles.tipsContent}>
                {cookingTips.map((tip, index) => (
                  <View key={index} style={styles.tipItem}>
                    <View style={styles.tipBullet} />
                    <Text style={styles.tipText}>{tip}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },

  // Header
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },

  // Error State
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  errorText: {
    fontSize: FontSizes.lg,
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.lg,
  },
  backButton: {
    backgroundColor: Colors.brand,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  backButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },

  // Recipe Header
  recipeHeader: {
    padding: Spacing.lg,
  },
  recipeTitle: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  recipeDescription: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    lineHeight: 24,
    marginBottom: Spacing.lg,
  },
  recipeStats: {
    flexDirection: 'row',
    gap: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  statText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  difficultyItem: {
    marginLeft: 'auto',
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: BorderRadius.full,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
  },
  tag: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  tagText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },

  // Sections
  section: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Modern Servings
  servingsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  servingsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  servingsAdjuster: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  servingsButton: {
    width: 36,
    height: 36,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  servingsText: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginHorizontal: Spacing.lg,
    minWidth: 40,
    textAlign: 'center',
  },

  // Ingredients
  ingredientsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  ingredientBullet: {
    width: 6,
    height: 6,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brand,
  },
  ingredientText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Instructions
  instructionsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: Spacing.md,
    gap: Spacing.md,
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionNumberText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  instructionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 22,
  },

  // Nutrition
  nutritionCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.lg,
  },
  nutritionItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
  },
  nutritionLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  nutritionValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },

  // Actions
  actionSection: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
    gap: Spacing.md,
  },
  primaryAction: {
    backgroundColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  primaryActionText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },
  secondaryAction: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  secondaryActionText: {
    color: Colors.brand,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Enhanced Recipe Styles
  servingsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  nutritionPreview: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  nutritionPreviewText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  servingsDisplay: {
    alignItems: 'center',
    minWidth: 80,
  },
  servingsLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  servingsButtonDisabled: {
    opacity: 0.5,
  },
  servingPresets: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.sm,
    marginTop: Spacing.md,
  },
  presetButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background,
  },
  presetButtonActive: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  presetButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },
  presetButtonTextActive: {
    color: Colors.brandForeground,
  },
  scalingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.md,
    gap: Spacing.xs,
  },
  scalingText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontStyle: 'italic',
  },

  // Enhanced Ingredients
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  ingredientStats: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  ingredientStatsText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  ingredientCheckbox: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.sm,
    borderWidth: 2,
    borderColor: Colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  ingredientCheckboxChecked: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  ingredientItemChecked: {
    backgroundColor: Colors.muted + '30',
  },
  ingredientTextChecked: {
    textDecorationLine: 'line-through',
    color: Colors.mutedForeground,
  },

  // Enhanced Nutrition
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: Spacing.md,
  },
  nutritionExpanded: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.md,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: Spacing.xs,
  },
  nutritionDetailLabel: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
  },
  nutritionDetailValue: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },
  nutritionProgress: {
    marginTop: Spacing.md,
  },
  nutritionProgressTitle: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  progressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  progressLabel: {
    fontSize: FontSizes.xs,
    color: Colors.foreground,
    flex: 1,
  },
  progressBarContainer: {
    flex: 2,
    height: 6,
    backgroundColor: Colors.muted,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: 3,
  },
  progressValue: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
    minWidth: 35,
    textAlign: 'right',
  },

  // Ultra Modern Action Styles
  actionSection: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
    gap: Spacing.lg,
  },

  // Grid Layout
  secondaryActionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  gridButton: {
    flex: 1,
  },

  // Tips Container - Ultra Modern
  tipsContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  tipsHeaderIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.accentLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  tipsHeaderTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  tipsContent: {
    gap: Spacing.sm,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tipBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.accent,
    marginTop: 8,
    marginRight: Spacing.sm,
  },
  tipText: {
    flex: 1,
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
});

export default RecipeDetailScreen;
