import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Dimensions,
  StatusBar,
  ImageBackground,
  Pressable,
  Haptics,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeInUp, 
  FadeInDown, 
  FadeInLeft, 
  FadeInRight,
  SlideInUp,
  SlideInDown,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';

const { width, height } = Dimensions.get('window');

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  image?: string;
  rating?: number;
  servings?: number;
  prepTime?: string;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface IngredientItemProps {
  ingredient: string;
  checked: boolean;
  onToggle: () => void;
  servings: number;
  originalServings: number;
  index: number;
}

interface InstructionStepProps {
  instruction: string;
  stepNumber: number;
  isActive: boolean;
  isCompleted: boolean;
  onPress: () => void;
  onTimerPress: () => void;
  index: number;
}

interface NutritionRingProps {
  label: string;
  value: number;
  maxValue: number;
  color: string;
  unit: string;
  index: number;
}

// Modern Ingredient Item Component
const IngredientItem: React.FC<IngredientItemProps> = ({
  ingredient,
  checked,
  onToggle,
  servings,
  originalServings,
  index,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    opacity.value = withSequence(
      withTiming(0.7, { duration: 100 }),
      withTiming(checked ? 0.6 : 1, { duration: 100 })
    );
    runOnJS(onToggle)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const scaleIngredient = (ingredient: string) => {
    if (servings === originalServings) return ingredient;
    
    const scaleFactor = servings / originalServings;
    return ingredient.replace(/(\d+(?:\.\d+)?)\s*(\w+)/g, (match, amount, unit) => {
      const scaledAmount = (parseFloat(amount) * scaleFactor).toFixed(1);
      return `${scaledAmount} ${unit}`;
    });
  };

  return (
    <Animated.View 
      entering={SlideInLeft.delay(index * 100).duration(500)}
      style={[styles.ingredientItem, animatedStyle]}
    >
      <Pressable style={styles.ingredientButton} onPress={handlePress}>
        <View style={[styles.checkbox, checked && styles.checkboxChecked]}>
          {checked && (
            <Animated.View entering={ZoomIn.duration(200)}>
              <Ionicons name="checkmark" size={16} color={Colors.brandForeground} />
            </Animated.View>
          )}
        </View>
        <Text style={[styles.ingredientText, checked && styles.ingredientTextChecked]}>
          {scaleIngredient(ingredient)}
        </Text>
      </Pressable>
    </Animated.View>
  );
};

// Modern Instruction Step Component
const InstructionStep: React.FC<InstructionStepProps> = ({
  instruction,
  stepNumber,
  isActive,
  isCompleted,
  onPress,
  onTimerPress,
  index,
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.98, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const hasTimer = instruction.toLowerCase().includes('minute') || 
                   instruction.toLowerCase().includes('hour') ||
                   instruction.toLowerCase().includes('cook') ||
                   instruction.toLowerCase().includes('bake');

  return (
    <Animated.View 
      entering={SlideInRight.delay(index * 150).duration(600)}
      style={[
        styles.instructionStep,
        isActive && styles.instructionStepActive,
        isCompleted && styles.instructionStepCompleted,
        animatedStyle
      ]}
    >
      <Pressable style={styles.instructionButton} onPress={handlePress}>
        <View style={styles.stepHeader}>
          <View style={[
            styles.stepNumber,
            isActive && styles.stepNumberActive,
            isCompleted && styles.stepNumberCompleted
          ]}>
            {isCompleted ? (
              <Ionicons name="checkmark" size={16} color={Colors.brandForeground} />
            ) : (
              <Text style={[
                styles.stepNumberText,
                isActive && styles.stepNumberTextActive
              ]}>
                {stepNumber}
              </Text>
            )}
          </View>
          
          <View style={styles.stepContent}>
            <Text style={[
              styles.instructionText,
              isActive && styles.instructionTextActive,
              isCompleted && styles.instructionTextCompleted
            ]}>
              {instruction}
            </Text>
          </View>

          {hasTimer && (
            <TouchableOpacity 
              style={styles.timerButton}
              onPress={onTimerPress}
              onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)}
            >
              <Ionicons name="timer" size={20} color={Colors.brand} />
            </TouchableOpacity>
          )}
        </View>
      </Pressable>
    </Animated.View>
  );
};

// Nutrition Ring Component
const NutritionRing: React.FC<NutritionRingProps> = ({
  label,
  value,
  maxValue,
  color,
  unit,
  index,
}) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withDelay(index * 200, withTiming(value / maxValue, { duration: 1000 }));
  }, [value, maxValue, index]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${progress.value * 360}deg` }],
  }));

  return (
    <Animated.View 
      entering={ZoomIn.delay(index * 100).duration(600)}
      style={styles.nutritionRing}
    >
      <View style={styles.ringContainer}>
        <View style={[styles.ringBackground, { borderColor: color + '20' }]} />
        <Animated.View 
          style={[
            styles.ringProgress, 
            { borderColor: color },
            animatedStyle
          ]} 
        />
        <View style={styles.ringCenter}>
          <Text style={[styles.ringValue, { color }]}>{value}</Text>
          <Text style={styles.ringUnit}>{unit}</Text>
        </View>
      </View>
      <Text style={styles.ringLabel}>{label}</Text>
    </Animated.View>
  );
};

const RecipeDetailScreenModern: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const recipe = route.params?.recipe as Recipe;

  const [servings, setServings] = useState(recipe?.servings || 4);
  const [checkedIngredients, setCheckedIngredients] = useState<boolean[]>(
    new Array(recipe?.ingredients.length || 0).fill(false)
  );
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(
    new Array(recipe?.instructions.length || 0).fill(false)
  );
  const [showNutrition, setShowNutrition] = useState(false);
  const [showTimer, setShowTimer] = useState(false);
  const [timerStep, setTimerStep] = useState(0);
  const [isSaved, setIsSaved] = useState(false);

  const scrollViewRef = useRef<ScrollView>(null);

  if (!recipe) {
    return (
      <View style={styles.container}>
        <Text>Recipe not found</Text>
      </View>
    );
  }

  const handleIngredientToggle = (index: number) => {
    const newChecked = [...checkedIngredients];
    newChecked[index] = !newChecked[index];
    setCheckedIngredients(newChecked);
  };

  const handleStepPress = (index: number) => {
    setActiveStep(index);
    const newCompleted = [...completedSteps];
    newCompleted[index] = !newCompleted[index];
    setCompletedSteps(newCompleted);
  };

  const handleTimerPress = (stepIndex: number) => {
    setTimerStep(stepIndex);
    setShowTimer(true);
  };

  const startCooking = () => {
    navigation.navigate('CookingTimer' as never, {
      recipe: recipe.title,
      instructions: recipe.instructions,
      servings
    } as never);
  };

  const shareRecipe = async () => {
    try {
      await Share.share({
        message: `Check out this amazing recipe: ${recipe.title}\n\n${recipe.description}`,
        title: recipe.title,
      });
    } catch (error) {
      console.error('Error sharing recipe:', error);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return Colors.success;
      case 'Medium': return Colors.warning;
      case 'Hard': return Colors.error;
      default: return Colors.mutedForeground;
    }
  };

  const getCompletionPercentage = () => {
    const completed = completedSteps.filter(Boolean).length;
    return Math.round((completed / completedSteps.length) * 100);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Hero Header */}
      <View style={styles.heroSection}>
        <ImageBackground
          source={{ uri: recipe.image || 'https://via.placeholder.com/400x300' }}
          style={styles.heroImage}
          resizeMode="cover"
        >
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.8)']}
            style={styles.heroGradient}
          >
            <View style={styles.heroHeader}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Ionicons name="arrow-back" size={24} color={Colors.brandForeground} />
              </TouchableOpacity>
              
              <View style={styles.heroActions}>
                <TouchableOpacity 
                  style={[styles.actionButton, isSaved && styles.actionButtonActive]}
                  onPress={() => setIsSaved(!isSaved)}
                >
                  <Ionicons 
                    name={isSaved ? "heart" : "heart-outline"} 
                    size={24} 
                    color={isSaved ? Colors.error : Colors.brandForeground} 
                  />
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.actionButton}
                  onPress={shareRecipe}
                >
                  <Ionicons name="share" size={24} color={Colors.brandForeground} />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.heroContent}>
              <Animated.View entering={FadeInUp.delay(300).duration(800)}>
                <Text style={styles.heroTitle}>{recipe.title}</Text>
                <Text style={styles.heroDescription}>{recipe.description}</Text>
              </Animated.View>

              <Animated.View 
                entering={FadeInUp.delay(500).duration(800)}
                style={styles.heroStats}
              >
                <View style={styles.statItem}>
                  <Ionicons name="time" size={20} color={Colors.brandForeground} />
                  <Text style={styles.statText}>{recipe.cookTime}</Text>
                </View>
                <View style={styles.statItem}>
                  <Ionicons name="flame" size={20} color={Colors.brandForeground} />
                  <Text style={styles.statText}>{recipe.calories} cal</Text>
                </View>
                <View style={styles.statItem}>
                  <View style={[styles.difficultyDot, { backgroundColor: getDifficultyColor(recipe.difficulty) }]} />
                  <Text style={styles.statText}>{recipe.difficulty}</Text>
                </View>
                <View style={styles.statItem}>
                  <Ionicons name="people" size={20} color={Colors.brandForeground} />
                  <Text style={styles.statText}>{servings} servings</Text>
                </View>
              </Animated.View>
            </View>
          </LinearGradient>
        </ImageBackground>
      </View>

      {/* Main Content */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Servings Adjuster */}
        <Animated.View entering={SlideInUp.delay(200).duration(600)} style={styles.servingsSection}>
          <ModernCard variant="glass" style={styles.servingsCard}>
            <View style={styles.servingsHeader}>
              <Text style={styles.servingsTitle}>Servings</Text>
              <Text style={styles.servingsSubtitle}>Adjust recipe quantities</Text>
            </View>

            <View style={styles.servingsControls}>
              <TouchableOpacity
                style={[styles.servingsButton, servings <= 1 && styles.servingsButtonDisabled]}
                onPress={() => servings > 1 && setServings(servings - 1)}
                disabled={servings <= 1}
              >
                <Ionicons name="remove" size={20} color={servings <= 1 ? Colors.mutedForeground : Colors.brand} />
              </TouchableOpacity>

              <View style={styles.servingsDisplay}>
                <Text style={styles.servingsNumber}>{servings}</Text>
              </View>

              <TouchableOpacity
                style={[styles.servingsButton, servings >= 12 && styles.servingsButtonDisabled]}
                onPress={() => servings < 12 && setServings(servings + 1)}
                disabled={servings >= 12}
              >
                <Ionicons name="add" size={20} color={servings >= 12 ? Colors.mutedForeground : Colors.brand} />
              </TouchableOpacity>
            </View>
          </ModernCard>
        </Animated.View>

        {/* Ingredients Section */}
        <Animated.View entering={SlideInUp.delay(300).duration(600)} style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Ingredients</Text>
            <View style={styles.progressIndicator}>
              <Text style={styles.progressText}>
                {checkedIngredients.filter(Boolean).length}/{recipe.ingredients.length}
              </Text>
            </View>
          </View>

          <ModernCard variant="default" style={styles.ingredientsCard}>
            {recipe.ingredients.map((ingredient, index) => (
              <IngredientItem
                key={index}
                ingredient={ingredient}
                checked={checkedIngredients[index]}
                onToggle={() => handleIngredientToggle(index)}
                servings={servings}
                originalServings={recipe.servings || 4}
                index={index}
              />
            ))}
          </ModernCard>
        </Animated.View>

        {/* Instructions Section */}
        <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Instructions</Text>
            <View style={styles.progressIndicator}>
              <Text style={styles.progressText}>{getCompletionPercentage()}% complete</Text>
            </View>
          </View>

          <View style={styles.instructionsContainer}>
            {recipe.instructions.map((instruction, index) => (
              <InstructionStep
                key={index}
                instruction={instruction}
                stepNumber={index + 1}
                isActive={activeStep === index}
                isCompleted={completedSteps[index]}
                onPress={() => handleStepPress(index)}
                onTimerPress={() => handleTimerPress(index)}
                index={index}
              />
            ))}
          </View>
        </Animated.View>

        {/* Nutrition Section */}
        {recipe.nutrition && (
          <Animated.View entering={SlideInUp.delay(500).duration(600)} style={styles.section}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setShowNutrition(!showNutrition)}
            >
              <Text style={styles.sectionTitle}>Nutrition Facts</Text>
              <Ionicons
                name={showNutrition ? "chevron-up" : "chevron-down"}
                size={20}
                color={Colors.mutedForeground}
              />
            </TouchableOpacity>

            {showNutrition && (
              <Animated.View entering={SlideInUp.duration(400)} style={styles.nutritionContainer}>
                <ModernCard variant="glass" style={styles.nutritionCard}>
                  <View style={styles.nutritionGrid}>
                    <NutritionRing
                      label="Protein"
                      value={recipe.nutrition.protein}
                      maxValue={100}
                      color={Colors.success}
                      unit="g"
                      index={0}
                    />
                    <NutritionRing
                      label="Carbs"
                      value={recipe.nutrition.carbs}
                      maxValue={150}
                      color={Colors.warning}
                      unit="g"
                      index={1}
                    />
                    <NutritionRing
                      label="Fat"
                      value={recipe.nutrition.fat}
                      maxValue={80}
                      color={Colors.error}
                      unit="g"
                      index={2}
                    />
                    <NutritionRing
                      label="Fiber"
                      value={recipe.nutrition.fiber}
                      maxValue={30}
                      color={Colors.info}
                      unit="g"
                      index={3}
                    />
                  </View>
                </ModernCard>
              </Animated.View>
            )}
          </Animated.View>
        )}

        {/* Action Buttons */}
        <Animated.View entering={SlideInUp.delay(600).duration(600)} style={styles.actionSection}>
          <ModernButton
            title="Start Cooking"
            onPress={startCooking}
            variant="primary"
            size="xl"
            icon="restaurant"
            fullWidth
            style={styles.primaryAction}
          />

          <View style={styles.secondaryActions}>
            <ModernButton
              title="Add to Cart"
              onPress={() => {}}
              variant="outline"
              size="md"
              icon="cart"
              style={styles.secondaryAction}
            />
            <ModernButton
              title="Set Timer"
              onPress={() => setShowTimer(true)}
              variant="outline"
              size="md"
              icon="timer"
              style={styles.secondaryAction}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Timer Modal */}
      <ModernModal
        visible={showTimer}
        onClose={() => setShowTimer(false)}
        title="Set Cooking Timer"
        variant="center"
        size="md"
      >
        <View style={styles.timerModalContent}>
          <Text style={styles.timerModalText}>
            Timer for step {timerStep + 1}
          </Text>
          <ModernButton
            title="Start 15 min timer"
            onPress={() => {
              setShowTimer(false);
              // Navigate to timer screen
            }}
            variant="primary"
            size="md"
            icon="timer"
          />
        </View>
      </ModernModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  // Hero Section
  heroSection: {
    height: height * 0.5,
    position: 'relative',
  },
  heroImage: {
    flex: 1,
    width: '100%',
  },
  heroGradient: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 60, // Status bar
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  heroContent: {
    gap: Spacing.lg,
  },
  heroTitle: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginBottom: Spacing.md,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  heroDescription: {
    fontSize: FontSizes.lg,
    color: Colors.brandForeground,
    opacity: 0.9,
    lineHeight: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  heroStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.lg,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  statText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brandForeground,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Main Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },

  // Servings Section
  servingsSection: {
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  servingsCard: {
    padding: Spacing.xl,
  },
  servingsHeader: {
    marginBottom: Spacing.lg,
  },
  servingsTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  servingsSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
  },
  servingsControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.xl,
  },
  servingsButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  servingsButtonDisabled: {
    backgroundColor: Colors.muted,
  },
  servingsDisplay: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },
  servingsNumber: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },

  // Section Styles
  section: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xxl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  progressIndicator: {
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  progressText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Ingredients
  ingredientsCard: {
    padding: Spacing.lg,
  },
  ingredientItem: {
    marginBottom: Spacing.md,
  },
  ingredientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.md,
    borderWidth: 2,
    borderColor: Colors.border,
    marginRight: Spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  ingredientText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 20,
  },
  ingredientTextChecked: {
    color: Colors.mutedForeground,
    textDecorationLine: 'line-through',
  },

  // Instructions
  instructionsContainer: {
    gap: Spacing.lg,
  },
  instructionStep: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  instructionStepActive: {
    borderColor: Colors.brand,
    backgroundColor: Colors.brandMuted,
  },
  instructionStepCompleted: {
    backgroundColor: Colors.successMuted,
    borderColor: Colors.success,
  },
  instructionButton: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.lg,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  stepNumberActive: {
    backgroundColor: Colors.brand,
  },
  stepNumberCompleted: {
    backgroundColor: Colors.success,
  },
  stepNumberText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.mutedForeground,
  },
  stepNumberTextActive: {
    color: Colors.brandForeground,
  },
  stepContent: {
    flex: 1,
  },
  instructionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    lineHeight: 24,
  },
  instructionTextActive: {
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },
  instructionTextCompleted: {
    color: Colors.mutedForeground,
  },
  timerButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Nutrition
  nutritionContainer: {
    marginTop: Spacing.lg,
  },
  nutritionCard: {
    padding: Spacing.xl,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: Spacing.lg,
  },
  nutritionRing: {
    alignItems: 'center',
    width: 80,
  },
  ringContainer: {
    width: 60,
    height: 60,
    position: 'relative',
    marginBottom: Spacing.md,
  },
  ringBackground: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 4,
  },
  ringProgress: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 4,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  ringCenter: {
    position: 'absolute',
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ringValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
  },
  ringUnit: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  ringLabel: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    textAlign: 'center',
    fontWeight: FontWeights.medium,
  },

  // Actions
  actionSection: {
    paddingHorizontal: Spacing.xl,
    gap: Spacing.lg,
  },
  primaryAction: {
    marginBottom: Spacing.md,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  secondaryAction: {
    flex: 1,
  },

  // Timer Modal
  timerModalContent: {
    padding: Spacing.xl,
    alignItems: 'center',
    gap: Spacing.lg,
  },
  timerModalText: {
    fontSize: FontSizes.lg,
    color: Colors.foreground,
    textAlign: 'center',
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default RecipeDetailScreenModern;
