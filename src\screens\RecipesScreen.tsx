import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService, { Recipe } from '../services/ApiService';

const RecipesScreen: React.FC = () => {
  const [query, setQuery] = useState('');
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [loading, setLoading] = useState(false);

  const handleGenerate = async () => {
    if (!query.trim()) return;

    setLoading(true);
    setRecipe(null);

    try {
      const result = await ApiService.generateRecipe(query);
      setRecipe(result);
    } catch (error) {
      Alert.alert('Error', 'Failed to generate recipe. Please try again.');
      console.error('Recipe generation error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.backgroundAlt]}
        style={styles.backgroundGradient}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
            <Text style={styles.title}>AI Recipe Chef</Text>
            <Text style={styles.subtitle}>Tell me what you have, I'll create a meal.</Text>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              value={query}
              onChangeText={setQuery}
              placeholder="e.g., 'chicken, rice, and broccoli' or 'a quick vegan snack'"
              placeholderTextColor={Colors.textLight}
              multiline
              numberOfLines={4}
            />
            
            <TouchableOpacity
              style={[styles.generateButton, loading && styles.generateButtonDisabled]}
              onPress={handleGenerate}
              disabled={loading}
            >
              <LinearGradient
                colors={loading ? [Colors.gray400, Colors.gray500] : [Colors.primary, Colors.secondary]}
                style={styles.buttonGradient}
              >
                <Ionicons 
                  name={loading ? "hourglass" : "restaurant"} 
                  size={24} 
                  color={Colors.white} 
                />
                <Text style={styles.buttonText}>
                  {loading ? 'Generating...' : 'Generate Recipe'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Recipe Results */}
          {recipe && (
            <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.recipeContainer}>
              <View style={styles.recipeCard}>
                <Text style={styles.recipeTitle}>{recipe.recipeTitle}</Text>

                <View style={styles.nutritionInfo}>
                  <View style={styles.nutritionChip}>
                    <Ionicons name="flame" size={16} color={Colors.primary} />
                    <Text style={styles.nutritionText}>{recipe.estimatedCalories} cal</Text>
                  </View>
                  <View style={styles.nutritionChip}>
                    <Text style={styles.nutritionText}>{recipe.macros.protein} protein</Text>
                  </View>
                  <View style={styles.nutritionChip}>
                    <Text style={styles.nutritionText}>{recipe.macros.carbs} carbs</Text>
                  </View>
                </View>

                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Ingredients</Text>
                  {recipe.ingredients.map((ingredient, index) => (
                    <Animated.View
                      key={index}
                      entering={FadeInUp.delay(700 + index * 50).duration(400)}
                      style={styles.ingredientRow}
                    >
                      <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                      <Text style={styles.ingredientText}>{ingredient}</Text>
                    </Animated.View>
                  ))}
                </View>

                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Instructions</Text>
                  {recipe.steps.map((step, index) => (
                    <Animated.View
                      key={index}
                      entering={FadeInUp.delay(800 + index * 100).duration(400)}
                      style={styles.stepRow}
                    >
                      <View style={styles.stepNumber}>
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <Text style={styles.stepText}>{step}</Text>
                    </Animated.View>
                  ))}
                </View>

                <View style={styles.tagsContainer}>
                  {recipe.tags.map((tag, index) => (
                    <View key={index} style={styles.tag}>
                      <Text style={styles.tagText}>{tag}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </Animated.View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    textAlign: 'center',
  },
  inputContainer: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  textInput: {
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: FontSizes.base,
    color: Colors.text,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: Spacing.md,
  },
  generateButton: {
    width: '100%',
  },
  generateButtonDisabled: {
    opacity: 0.7,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  buttonText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
  recipeContainer: {
    marginBottom: Spacing.xxl,
  },
  recipeCard: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  recipeTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  nutritionInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginBottom: Spacing.lg,
  },
  nutritionChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginHorizontal: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  nutritionText: {
    fontSize: FontSizes.sm,
    color: Colors.text,
    marginLeft: Spacing.xs,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  ingredientText: {
    fontSize: FontSizes.base,
    color: Colors.text,
    marginLeft: Spacing.sm,
    flex: 1,
  },
  stepRow: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.white,
  },
  stepText: {
    fontSize: FontSizes.base,
    color: Colors.text,
    flex: 1,
    lineHeight: 22,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: Spacing.md,
  },
  tag: {
    backgroundColor: Colors.primaryOpacity10,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  tagText: {
    fontSize: FontSizes.xs,
    color: Colors.primary,
    fontWeight: FontWeights.medium,
  },
});

export default RecipesScreen;
