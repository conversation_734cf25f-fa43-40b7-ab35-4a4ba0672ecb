import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeInLeft } from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService from '../services/ApiService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
}



const RecipesScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDietary, setSelectedDietary] = useState<string[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [maxCookTime, setMaxCookTime] = useState<string>('');
  const [availableIngredients, setAvailableIngredients] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'gluten-free', label: 'Gluten-Free', icon: 'shield-checkmark' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
  ];

  const difficultyOptions = [
    { id: 'easy', label: 'Easy', icon: 'happy' },
    { id: 'medium', label: 'Medium', icon: 'star' },
    { id: 'hard', label: 'Hard', icon: 'flame' },
  ];

  const timeOptions = [
    { id: '15', label: '15 min' },
    { id: '30', label: '30 min' },
    { id: '45', label: '45 min' },
    { id: '60', label: '1 hour' },
  ];

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText('');
        setIsListening(false);
        setSearchQuery(result.text);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const generateRecipes = async () => {
    setLoading(true);
    try {
      // Build enhanced query with filters
      let query = searchQuery || 'healthy meal';

      if (selectedDietary.length > 0) {
        query += ` that is ${selectedDietary.join(' and ')}`;
      }

      if (selectedDifficulty) {
        query += ` with ${selectedDifficulty} difficulty level`;
      }

      if (maxCookTime) {
        query += ` that takes maximum ${maxCookTime} minutes to cook`;
      }

      if (availableIngredients.length > 0) {
        query += ` using ingredients like ${availableIngredients.join(', ')}`;
      }

      console.log('🔍 Enhanced recipe query:', query);
      const result = await ApiService.generateRecipe(query);

      // Convert single recipe to array format
      const recipeWithId = {
        id: Date.now().toString(),
        title: result.recipeTitle,
        description: `A delicious ${query} recipe with ${result.estimatedCalories} calories`,
        cookTime: '25 min', // Default value
        difficulty: 'Easy' as const,
        calories: result.estimatedCalories,
        ingredients: result.ingredients,
        instructions: result.steps,
        tags: result.tags
      };

      setRecipes([recipeWithId]);
    } catch (error) {
      console.error('Error generating recipes:', error);
      // Fallback to mock data
      setRecipes([
        {
          id: '1',
          title: 'Mediterranean Quinoa Bowl',
          description: 'A nutritious bowl packed with fresh vegetables, quinoa, and olive oil dressing',
          cookTime: '25 min',
          difficulty: 'Easy',
          calories: 420,
          ingredients: ['Quinoa', 'Cherry tomatoes', 'Cucumber', 'Feta cheese', 'Olive oil', 'Lemon'],
          instructions: ['Cook quinoa', 'Chop vegetables', 'Mix with dressing', 'Serve fresh'],
          tags: ['Healthy', 'Mediterranean', 'Vegetarian']
        },
        {
          id: '2',
          title: 'Grilled Salmon with Asparagus',
          description: 'Perfectly grilled salmon with roasted asparagus and herbs',
          cookTime: '20 min',
          difficulty: 'Medium',
          calories: 380,
          ingredients: ['Salmon fillet', 'Asparagus', 'Lemon', 'Herbs', 'Olive oil'],
          instructions: ['Season salmon', 'Grill for 6-8 minutes', 'Roast asparagus', 'Serve with lemon'],
          tags: ['High Protein', 'Low Carb', 'Omega-3']
        }
      ]);
    }
    setLoading(false);
  };

  const toggleDietary = (option: string) => {
    setSelectedDietary(prev =>
      prev.includes(option)
        ? prev.filter(item => item !== option)
        : [...prev, option]
    );
  };

  const DietaryChip: React.FC<{ option: any; isSelected: boolean; onPress: () => void }> = ({
    option,
    isSelected,
    onPress
  }) => (
    <TouchableOpacity
      style={[styles.dietaryChip, isSelected && styles.dietaryChipSelected]}
      onPress={onPress}
    >
      <Ionicons
        name={option.icon as any}
        size={16}
        color={isSelected ? Colors.brandForeground : Colors.brand}
      />
      <Text style={[
        styles.dietaryChipText,
        isSelected && styles.dietaryChipTextSelected
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const RecipeCard: React.FC<{ recipe: Recipe; delay: number }> = ({ recipe, delay }) => {
    const cardNavigation = useNavigation();

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
        <TouchableOpacity style={styles.recipeCard}>
          <View style={styles.recipeHeader}>
            <View style={styles.recipeInfo}>
              <Text style={styles.recipeTitle}>{recipe.title}</Text>
              <Text style={styles.recipeDescription}>{recipe.description}</Text>
            </View>
            <View style={styles.recipeStats}>
              <View style={styles.statItem}>
                <Ionicons name="time" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.cookTime}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons name="flame" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.calories} cal</Text>
              </View>
            </View>
          </View>

          <View style={styles.recipeTags}>
            {recipe.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            <View style={[styles.tag, styles.difficultyTag]}>
              <Text style={styles.difficultyText}>{recipe.difficulty}</Text>
            </View>
          </View>

          <View style={styles.recipeFooter}>
            <Text style={styles.ingredientsLabel}>
              {recipe.ingredients.length} ingredients
            </Text>
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => cardNavigation.navigate('RecipeDetail' as never, { recipe } as never)}
            >
              <Text style={styles.viewButtonText}>View Recipe</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
          <Text style={styles.title}>AI Recipes</Text>
          <Text style={styles.subtitle}>Discover personalized recipes just for you</Text>
        </Animated.View>

        {/* Search Bar */}
        <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color={Colors.mutedForeground} />
            <TextInput
              style={styles.searchInput}
              placeholder="What would you like to cook?"
              placeholderTextColor={Colors.mutedForeground}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />

            {/* Voice button for recipe search */}
            <TouchableOpacity
              style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
              onPress={isListening ? stopVoiceRecognition : startVoiceRecognition}
            >
              <Ionicons
                name={isListening ? "stop" : "mic"}
                size={18}
                color={Colors.brandForeground}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.filterToggle}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons
                name={showFilters ? "options" : "options-outline"}
                size={20}
                color={Colors.brand}
              />
            </TouchableOpacity>
          </View>

          {/* Voice feedback text */}
          {voiceText && (
            <Animated.View entering={FadeInUp.duration(300)} style={styles.voiceTextContainer}>
              <Text style={styles.voiceText}>{voiceText}</Text>
            </Animated.View>
          )}
        </Animated.View>

        {/* Enhanced Filters */}
        {showFilters && (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.filtersContainer}>
            {/* Difficulty Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Difficulty Level</Text>
              <View style={styles.filterOptions}>
                {difficultyOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterChip,
                      selectedDifficulty === option.id && styles.filterChipSelected
                    ]}
                    onPress={() => setSelectedDifficulty(
                      selectedDifficulty === option.id ? '' : option.id
                    )}
                  >
                    <Ionicons
                      name={option.icon as any}
                      size={16}
                      color={selectedDifficulty === option.id ? Colors.brandForeground : Colors.brand}
                    />
                    <Text style={[
                      styles.filterChipText,
                      selectedDifficulty === option.id && styles.filterChipTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Cook Time Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Max Cook Time</Text>
              <View style={styles.filterOptions}>
                {timeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterChip,
                      maxCookTime === option.id && styles.filterChipSelected
                    ]}
                    onPress={() => setMaxCookTime(
                      maxCookTime === option.id ? '' : option.id
                    )}
                  >
                    <Text style={[
                      styles.filterChipText,
                      maxCookTime === option.id && styles.filterChipTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Clear Filters */}
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={() => {
                setSelectedDifficulty('');
                setMaxCookTime('');
                setSelectedDietary([]);
                setAvailableIngredients([]);
              }}
            >
              <Text style={styles.clearFiltersText}>Clear All Filters</Text>
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Dietary Preferences */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.dietaryContainer}
          >
            {dietaryOptions.map((option) => (
              <DietaryChip
                key={option.id}
                option={option}
                isSelected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietary(option.id)}
              />
            ))}
          </ScrollView>
        </Animated.View>

        {/* Generate Button */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.generateContainer}>
          <TouchableOpacity 
            style={[styles.generateButton, loading && styles.generateButtonDisabled]}
            onPress={generateRecipes}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.brandForeground} />
            ) : (
              <Ionicons name="sparkles" size={20} color={Colors.brandForeground} />
            )}
            <Text style={styles.generateButtonText}>
              {loading ? 'Generating...' : 'Generate Recipes'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Recipes List */}
        {recipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.section}>
            <Text style={styles.sectionTitle}>Your Recipes</Text>
            {recipes.map((recipe, index) => (
              <RecipeCard
                key={recipe.id}
                recipe={recipe}
                delay={1000 + index * 200}
              />
            ))}
          </Animated.View>
        )}

        {/* Quick Suggestions */}
        <Animated.View entering={FadeInUp.delay(1200).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Ideas</Text>
          <View style={styles.suggestionsGrid}>
            {['Breakfast', 'Lunch', 'Dinner', 'Snacks'].map((meal, index) => (
              <TouchableOpacity 
                key={meal} 
                style={styles.suggestionCard}
                onPress={() => setSearchQuery(meal.toLowerCase())}
              >
                <Ionicons 
                  name={
                    meal === 'Breakfast' ? 'sunny' :
                    meal === 'Lunch' ? 'restaurant' :
                    meal === 'Dinner' ? 'moon' : 'cafe'
                  } 
                  size={24} 
                  color={Colors.brand} 
                />
                <Text style={styles.suggestionText}>{meal}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },

  // Search Bar
  searchContainer: {
    marginBottom: Spacing.xl,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    paddingVertical: Spacing.sm,
  },
  voiceButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  voiceButtonActive: {
    backgroundColor: Colors.error,
  },
  voiceTextContainer: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.md,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  filterToggle: {
    padding: Spacing.sm,
    marginLeft: Spacing.sm,
  },

  // Enhanced Filters
  filtersContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  filterSection: {
    marginBottom: Spacing.lg,
  },
  filterTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.muted,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.xs,
  },
  filterChipSelected: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  filterChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  filterChipTextSelected: {
    color: Colors.brandForeground,
  },
  clearFiltersButton: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  clearFiltersText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
  },

  // Dietary Preferences
  dietaryContainer: {
    flexDirection: 'row',
  },
  dietaryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.brand,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    gap: Spacing.xs,
  },
  dietaryChipSelected: {
    backgroundColor: Colors.brand,
  },
  dietaryChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  dietaryChipTextSelected: {
    color: Colors.brandForeground,
  },

  // Generate Button
  generateContainer: {
    marginBottom: Spacing.xl,
  },
  generateButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    opacity: 0.6,
  },
  generateButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Recipe Cards
  recipeCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  recipeHeader: {
    marginBottom: Spacing.md,
  },
  recipeInfo: {
    marginBottom: Spacing.sm,
  },
  recipeTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  recipeDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  recipeStats: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  statText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginBottom: Spacing.md,
  },
  tag: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  tagText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  difficultyTag: {
    backgroundColor: Colors.muted,
  },
  difficultyText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ingredientsLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  viewButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },

  // Suggestions Grid
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  suggestionCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  suggestionText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
});

export default RecipesScreenModern;
