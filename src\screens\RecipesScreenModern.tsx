import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  ImageBackground,
  Pressable,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInRight,
  SlideInLeft,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput } from '../components/ModernInput';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import ApiService from '../services/ApiService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

const { width, height } = Dimensions.get('window');

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  image?: string;
  rating?: number;
  servings?: number;
  prepTime?: string;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface FilterChipProps {
  label: string;
  icon?: keyof typeof Ionicons.glyphMap;
  selected: boolean;
  onPress: () => void;
  variant?: 'default' | 'dietary' | 'difficulty' | 'time';
}

interface RecipeCardProps {
  recipe: Recipe;
  onPress: () => void;
  index: number;
  variant?: 'default' | 'featured' | 'compact';
}

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  onVoicePress: () => void;
  onFilterPress: () => void;
  isListening: boolean;
  voiceText: string;
}



// Modern Search Header Component
const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onVoicePress,
  onFilterPress,
  isListening,
  voiceText,
}) => {
  const voiceScale = useSharedValue(1);
  const voiceOpacity = useSharedValue(1);

  useEffect(() => {
    if (isListening) {
      voiceScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      voiceOpacity.value = withSequence(
        withTiming(0.7, { duration: 500 }),
        withTiming(1, { duration: 500 })
      );
    }
  }, [isListening]);

  const voiceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceScale.value }],
    opacity: voiceOpacity.value,
  }));

  return (
    <Animated.View entering={FadeInDown.duration(600)} style={styles.searchHeader}>
      <View style={styles.searchContainer}>
        <ModernInput
          value={searchQuery}
          onChangeText={onSearchChange}
          placeholder="Search recipes..."
          leftIcon="search"
          rightIcon={isListening ? "mic" : "mic-outline"}
          onRightIconPress={onVoicePress}
          variant="filled"
          style={styles.searchInput}
        />
        {voiceText && (
          <Animated.Text style={styles.voiceText}>{voiceText}</Animated.Text>
        )}
      </View>

      <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
        <Ionicons name="options" size={24} color={Colors.brand} />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Filter Chip Component
const FilterChip: React.FC<FilterChipProps> = ({
  label,
  icon,
  selected,
  onPress,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(selected ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    backgroundColor: interpolate(
      backgroundColor.value,
      [0, 1],
      [Colors.muted, Colors.brand]
    ),
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    backgroundColor.value = withTiming(selected ? 0 : 1, { duration: 200 });
    onPress();
  };

  return (
    <Animated.View style={[styles.filterChip, animatedStyle]}>
      <TouchableOpacity style={styles.filterChipButton} onPress={handlePress}>
        {icon && (
          <Ionicons
            name={icon}
            size={16}
            color={selected ? Colors.brandForeground : Colors.foreground}
          />
        )}
        <Text style={[
          styles.filterChipText,
          selected && styles.filterChipTextSelected
        ]}>
          {label}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Recipe Card Component
const RecipeCard: React.FC<RecipeCardProps> = ({
  recipe,
  onPress,
  index,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return Colors.success;
      case 'medium': return Colors.warning;
      case 'hard': return Colors.error;
      default: return Colors.mutedForeground;
    }
  };

  if (variant === 'featured') {
    return (
      <Animated.View
        entering={FadeInUp.delay(index * 100).duration(600)}
        style={[styles.featuredCard, animatedStyle, shadowAnimatedStyle]}
      >
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.featuredCardButton}
          activeOpacity={1}
        >
          <ImageBackground
            source={{ uri: recipe.image || 'https://via.placeholder.com/400x200' }}
            style={styles.featuredCardImage}
            imageStyle={styles.featuredCardImageStyle}
          >
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.featuredCardGradient}
            >
              <View style={styles.featuredCardContent}>
                <View style={styles.featuredCardBadges}>
                  <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                    <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
                  </View>
                  <View style={styles.timeBadge}>
                    <Ionicons name="time" size={12} color={Colors.brandForeground} />
                    <Text style={styles.timeBadgeText}>{recipe.cookTime}</Text>
                  </View>
                </View>

                <View style={styles.featuredCardInfo}>
                  <Text style={styles.featuredCardTitle}>{recipe.title}</Text>
                  <Text style={styles.featuredCardDescription}>{recipe.description}</Text>

                  <View style={styles.featuredCardStats}>
                    <View style={styles.statItem}>
                      <Ionicons name="flame" size={16} color={Colors.warning} />
                      <Text style={styles.statText}>{recipe.calories} cal</Text>
                    </View>
                    {recipe.rating && (
                      <View style={styles.statItem}>
                        <Ionicons name="star" size={16} color={Colors.warning} />
                        <Text style={styles.statText}>{recipe.rating}</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      entering={FadeInUp.delay(index * 100).duration(600)}
      style={[styles.recipeCard, animatedStyle, shadowAnimatedStyle]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.recipeCardButton}
        activeOpacity={1}
      >
        <View style={styles.recipeCardImage}>
          <ImageBackground
            source={{ uri: recipe.image || 'https://via.placeholder.com/150x150' }}
            style={styles.cardImageBackground}
            imageStyle={styles.cardImageStyle}
          >
            <View style={styles.cardImageOverlay}>
              <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
              </View>
            </View>
          </ImageBackground>
        </View>

        <View style={styles.recipeCardContent}>
          <Text style={styles.recipeCardTitle}>{recipe.title}</Text>
          <Text style={styles.recipeCardDescription} numberOfLines={2}>
            {recipe.description}
          </Text>

          <View style={styles.recipeCardStats}>
            <View style={styles.statItem}>
              <Ionicons name="time" size={14} color={Colors.mutedForeground} />
              <Text style={styles.statText}>{recipe.cookTime}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="flame" size={14} color={Colors.warning} />
              <Text style={styles.statText}>{recipe.calories} cal</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const RecipesScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDietary, setSelectedDietary] = useState<string[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [maxCookTime, setMaxCookTime] = useState<string>('');
  const [availableIngredients, setAvailableIngredients] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'relevance' | 'time' | 'calories' | 'rating'>('relevance');

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'gluten-free', label: 'Gluten-Free', icon: 'shield-checkmark' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
  ];

  const difficultyOptions = [
    { id: 'easy', label: 'Easy', icon: 'happy' },
    { id: 'medium', label: 'Medium', icon: 'star' },
    { id: 'hard', label: 'Hard', icon: 'flame' },
  ];

  const timeOptions = [
    { id: '15', label: '15 min' },
    { id: '30', label: '30 min' },
    { id: '45', label: '45 min' },
    { id: '60', label: '1 hour' },
  ];

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText('');
        setIsListening(false);
        setSearchQuery(result.text);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const generateRecipes = async () => {
    setLoading(true);
    try {
      // Build enhanced query with filters
      let query = searchQuery || 'healthy meal';

      if (selectedDietary.length > 0) {
        query += ` that is ${selectedDietary.join(' and ')}`;
      }

      if (selectedDifficulty) {
        query += ` with ${selectedDifficulty} difficulty level`;
      }

      if (maxCookTime) {
        query += ` that takes maximum ${maxCookTime} minutes to cook`;
      }

      if (availableIngredients.length > 0) {
        query += ` using ingredients like ${availableIngredients.join(', ')}`;
      }

      console.log('🔍 Enhanced recipe query:', query);
      const result = await ApiService.generateRecipe(query);

      // Convert single recipe to array format
      const recipeWithId = {
        id: Date.now().toString(),
        title: result.recipeTitle,
        description: `A delicious ${query} recipe with ${result.estimatedCalories} calories`,
        cookTime: '25 min', // Default value
        difficulty: 'Easy' as const,
        calories: result.estimatedCalories,
        ingredients: result.ingredients,
        instructions: result.steps,
        tags: result.tags
      };

      setRecipes([recipeWithId]);
    } catch (error) {
      console.error('Error generating recipes:', error);
      // Fallback to mock data
      setRecipes([
        {
          id: '1',
          title: 'Mediterranean Quinoa Bowl',
          description: 'A nutritious bowl packed with fresh vegetables, quinoa, and olive oil dressing',
          cookTime: '25 min',
          difficulty: 'Easy',
          calories: 420,
          ingredients: ['Quinoa', 'Cherry tomatoes', 'Cucumber', 'Feta cheese', 'Olive oil', 'Lemon'],
          instructions: ['Cook quinoa', 'Chop vegetables', 'Mix with dressing', 'Serve fresh'],
          tags: ['Healthy', 'Mediterranean', 'Vegetarian']
        },
        {
          id: '2',
          title: 'Grilled Salmon with Asparagus',
          description: 'Perfectly grilled salmon with roasted asparagus and herbs',
          cookTime: '20 min',
          difficulty: 'Medium',
          calories: 380,
          ingredients: ['Salmon fillet', 'Asparagus', 'Lemon', 'Herbs', 'Olive oil'],
          instructions: ['Season salmon', 'Grill for 6-8 minutes', 'Roast asparagus', 'Serve with lemon'],
          tags: ['High Protein', 'Low Carb', 'Omega-3']
        }
      ]);
    }
    setLoading(false);
  };

  const toggleDietary = (option: string) => {
    setSelectedDietary(prev =>
      prev.includes(option)
        ? prev.filter(item => item !== option)
        : [...prev, option]
    );
  };

  const DietaryChip: React.FC<{ option: any; isSelected: boolean; onPress: () => void }> = ({
    option,
    isSelected,
    onPress
  }) => (
    <TouchableOpacity
      style={[styles.dietaryChip, isSelected && styles.dietaryChipSelected]}
      onPress={onPress}
    >
      <Ionicons
        name={option.icon as any}
        size={16}
        color={isSelected ? Colors.brandForeground : Colors.brand}
      />
      <Text style={[
        styles.dietaryChipText,
        isSelected && styles.dietaryChipTextSelected
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const RecipeCard: React.FC<{ recipe: Recipe; delay: number }> = ({ recipe, delay }) => {
    const cardNavigation = useNavigation();

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
        <TouchableOpacity style={styles.recipeCard}>
          <View style={styles.recipeHeader}>
            <View style={styles.recipeInfo}>
              <Text style={styles.recipeTitle}>{recipe.title}</Text>
              <Text style={styles.recipeDescription}>{recipe.description}</Text>
            </View>
            <View style={styles.recipeStats}>
              <View style={styles.statItem}>
                <Ionicons name="time" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.cookTime}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons name="flame" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.calories} cal</Text>
              </View>
            </View>
          </View>

          <View style={styles.recipeTags}>
            {recipe.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            <View style={[styles.tag, styles.difficultyTag]}>
              <Text style={styles.difficultyText}>{recipe.difficulty}</Text>
            </View>
          </View>

          <View style={styles.recipeFooter}>
            <Text style={styles.ingredientsLabel}>
              {recipe.ingredients.length} ingredients
            </Text>
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => cardNavigation.navigate('RecipeDetail' as never, { recipe } as never)}
            >
              <Text style={styles.viewButtonText}>View Recipe</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const featuredRecipes = recipes.slice(0, 3);
  const regularRecipes = recipes.slice(3);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      {/* Search Header */}
      <SearchHeader
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onVoicePress={isListening ? stopVoiceRecognition : startVoiceRecognition}
        onFilterPress={() => setShowFilters(true)}
        isListening={isListening}
        voiceText={voiceText}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Filter Chips */}
        <Animated.View entering={FadeInLeft.delay(200).duration(600)} style={styles.filtersSection}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filtersContainer}
          >
            {dietaryOptions.map((option, index) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietaryFilter(option.id)}
                variant="dietary"
              />
            ))}
          </ScrollView>
        </Animated.View>

        {/* View Mode Toggle */}
        <Animated.View entering={FadeInRight.delay(300).duration(600)} style={styles.viewModeSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {recipes.length > 0 ? `${recipes.length} Recipes Found` : 'Discover Recipes'}
            </Text>
            <View style={styles.viewModeToggle}>
              <TouchableOpacity
                style={[styles.viewModeButton, viewMode === 'grid' && styles.viewModeButtonActive]}
                onPress={() => setViewMode('grid')}
              >
                <Ionicons
                  name="grid"
                  size={20}
                  color={viewMode === 'grid' ? Colors.brandForeground : Colors.mutedForeground}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.viewModeButton, viewMode === 'list' && styles.viewModeButtonActive]}
                onPress={() => setViewMode('list')}
              >
                <Ionicons
                  name="list"
                  size={20}
                  color={viewMode === 'list' ? Colors.brandForeground : Colors.mutedForeground}
                />
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

        {/* Loading State */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ModernLoading
              variant="dots"
              size="lg"
              text="Finding perfect recipes for you..."
            />
          </View>
        )}

        {/* Featured Recipes */}
        {featuredRecipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.featuredSection}>
            <Text style={styles.sectionTitle}>Featured Recipes</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.featuredContainer}
            >
              {featuredRecipes.map((recipe, index) => (
                <RecipeCard
                  key={recipe.id}
                  recipe={recipe}
                  onPress={() => navigation.navigate('RecipeDetail' as never, { recipe } as never)}
                  index={index}
                  variant="featured"
                />
              ))}
            </ScrollView>
          </Animated.View>
        )}

        {/* Regular Recipes */}
        {regularRecipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(500).duration(600)} style={styles.recipesSection}>
            <Text style={styles.sectionTitle}>All Recipes</Text>

            {viewMode === 'grid' ? (
              <View style={styles.recipesGrid}>
                {regularRecipes.map((recipe, index) => (
                  <RecipeCard
                    key={recipe.id}
                    recipe={recipe}
                    onPress={() => navigation.navigate('RecipeDetail' as never, { recipe } as never)}
                    index={index}
                    variant="default"
                  />
                ))}
              </View>
            ) : (
              <View style={styles.recipesList}>
                {regularRecipes.map((recipe, index) => (
                  <RecipeCard
                    key={recipe.id}
                    recipe={recipe}
                    onPress={() => navigation.navigate('RecipeDetail' as never, { recipe } as never)}
                    index={index}
                    variant="compact"
                  />
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {/* Empty State */}
        {!loading && recipes.length === 0 && (
          <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.emptyState}>
            <View style={styles.emptyStateIcon}>
              <Ionicons name="restaurant" size={60} color={Colors.mutedForeground} />
            </View>
            <Text style={styles.emptyStateTitle}>No recipes found</Text>
            <Text style={styles.emptyStateDescription}>
              Try adjusting your search or filters to discover new recipes
            </Text>
            <ModernButton
              title="Explore Popular Recipes"
              onPress={searchRecipes}
              variant="primary"
              size="lg"
              icon="search"
              style={styles.exploreButton}
            />
          </Animated.View>
        )}

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(700).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <ModernButton
              title="Search Recipes"
              onPress={searchRecipes}
              variant="primary"
              size="md"
              icon="search"
              style={styles.quickActionButton}
            />
            <ModernButton
              title="Random Recipe"
              onPress={getRandomRecipe}
              variant="secondary"
              size="md"
              icon="shuffle"
              style={styles.quickActionButton}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Filters Modal */}
      <ModernModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        title="Filter Recipes"
        variant="bottom"
        size="lg"
      >
        <View style={styles.filtersModalContent}>
          <Text style={styles.modalSectionTitle}>Dietary Preferences</Text>
          <View style={styles.modalFiltersGrid}>
            {dietaryOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietaryFilter(option.id)}
                variant="dietary"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Difficulty Level</Text>
          <View style={styles.modalFiltersGrid}>
            {difficultyOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDifficulty === option.id}
                onPress={() => setSelectedDifficulty(selectedDifficulty === option.id ? '' : option.id)}
                variant="difficulty"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Cooking Time</Text>
          <View style={styles.modalFiltersGrid}>
            {timeOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                selected={maxCookTime === option.id}
                onPress={() => setMaxCookTime(maxCookTime === option.id ? '' : option.id)}
                variant="time"
              />
            ))}
          </View>

          <View style={styles.modalButtons}>
            <ModernButton
              title="Clear All"
              onPress={clearFilters}
              variant="outline"
              size="md"
              style={styles.modalClearButton}
            />
            <ModernButton
              title="Apply Filters"
              onPress={() => {
                setShowFilters(false);
                searchRecipes();
              }}
              variant="primary"
              size="md"
              style={styles.modalApplyButton}
            />
          </View>
        </View>
      </ModernModal>
    </View>
  );
};

// Helper Functions
const toggleDietaryFilter = (filterId: string) => {
  setSelectedDietary(prev =>
    prev.includes(filterId)
      ? prev.filter(id => id !== filterId)
      : [...prev, filterId]
  );
};

const clearFilters = () => {
  setSelectedDietary([]);
  setSelectedDifficulty('');
  setMaxCookTime('');
};

const getRandomRecipe = async () => {
  setLoading(true);
  try {
    const randomRecipes = await ApiService.getRandomRecipes(1);
    if (randomRecipes.length > 0) {
      navigation.navigate('RecipeDetail' as never, { recipe: randomRecipes[0] } as never);
    }
  } catch (error) {
    Alert.alert('Error', 'Failed to get random recipe');
  } finally {
    setLoading(false);
  }
};

        {/* Enhanced Filters */}
        {showFilters && (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.filtersContainer}>
            {/* Difficulty Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Difficulty Level</Text>
              <View style={styles.filterOptions}>
                {difficultyOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterChip,
                      selectedDifficulty === option.id && styles.filterChipSelected
                    ]}
                    onPress={() => setSelectedDifficulty(
                      selectedDifficulty === option.id ? '' : option.id
                    )}
                  >
                    <Ionicons
                      name={option.icon as any}
                      size={16}
                      color={selectedDifficulty === option.id ? Colors.brandForeground : Colors.brand}
                    />
                    <Text style={[
                      styles.filterChipText,
                      selectedDifficulty === option.id && styles.filterChipTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Cook Time Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Max Cook Time</Text>
              <View style={styles.filterOptions}>
                {timeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterChip,
                      maxCookTime === option.id && styles.filterChipSelected
                    ]}
                    onPress={() => setMaxCookTime(
                      maxCookTime === option.id ? '' : option.id
                    )}
                  >
                    <Text style={[
                      styles.filterChipText,
                      maxCookTime === option.id && styles.filterChipTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Clear Filters */}
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={() => {
                setSelectedDifficulty('');
                setMaxCookTime('');
                setSelectedDietary([]);
                setAvailableIngredients([]);
              }}
            >
              <Text style={styles.clearFiltersText}>Clear All Filters</Text>
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Dietary Preferences */}
        <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.dietaryContainer}
          >
            {dietaryOptions.map((option) => (
              <DietaryChip
                key={option.id}
                option={option}
                isSelected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietary(option.id)}
              />
            ))}
          </ScrollView>
        </Animated.View>

        {/* Generate Button */}
        <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.generateContainer}>
          <TouchableOpacity 
            style={[styles.generateButton, loading && styles.generateButtonDisabled]}
            onPress={generateRecipes}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.brandForeground} />
            ) : (
              <Ionicons name="sparkles" size={20} color={Colors.brandForeground} />
            )}
            <Text style={styles.generateButtonText}>
              {loading ? 'Generating...' : 'Generate Recipes'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Recipes List */}
        {recipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.section}>
            <Text style={styles.sectionTitle}>Your Recipes</Text>
            {recipes.map((recipe, index) => (
              <RecipeCard
                key={recipe.id}
                recipe={recipe}
                delay={1000 + index * 200}
              />
            ))}
          </Animated.View>
        )}

        {/* Quick Suggestions */}
        <Animated.View entering={FadeInUp.delay(1200).duration(800)} style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Ideas</Text>
          <View style={styles.suggestionsGrid}>
            {['Breakfast', 'Lunch', 'Dinner', 'Snacks'].map((meal, index) => (
              <TouchableOpacity 
                key={meal} 
                style={styles.suggestionCard}
                onPress={() => setSearchQuery(meal.toLowerCase())}
              >
                <Ionicons 
                  name={
                    meal === 'Breakfast' ? 'sunny' :
                    meal === 'Lunch' ? 'restaurant' :
                    meal === 'Dinner' ? 'moon' : 'cafe'
                  } 
                  size={24} 
                  color={Colors.brand} 
                />
                <Text style={styles.suggestionText}>{meal}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Space for tab bar
  },

  // Search Header
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    backgroundColor: Colors.background,
    gap: Spacing.md,
  },
  searchContainer: {
    flex: 1,
  },
  searchInput: {
    marginBottom: 0,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontStyle: 'italic',
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  filterButton: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.sm,
  },

  // Filter Chips
  filtersSection: {
    marginBottom: Spacing.lg,
  },
  filtersContainer: {
    paddingHorizontal: Spacing.xl,
    gap: Spacing.md,
  },
  filterChip: {
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    marginRight: Spacing.md,
    ...Shadows.xs,
  },
  filterChipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  filterChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  filterChipTextSelected: {
    color: Colors.brandForeground,
  },

  // View Mode Section
  viewModeSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
  },
  viewModeButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  viewModeButtonActive: {
    backgroundColor: Colors.brand,
  },

  // Loading
  loadingContainer: {
    paddingVertical: Spacing.xxxl,
    alignItems: 'center',
  },

  // Featured Section
  featuredSection: {
    marginBottom: Spacing.xxl,
  },
  featuredContainer: {
    paddingHorizontal: Spacing.xl,
    gap: Spacing.lg,
  },
  featuredCard: {
    width: width * 0.85,
    height: 250,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    marginRight: Spacing.lg,
    ...Shadows.lg,
  },
  featuredCardButton: {
    flex: 1,
  },
  featuredCardImage: {
    flex: 1,
  },
  featuredCardImageStyle: {
    borderRadius: BorderRadius.xl,
  },
  featuredCardGradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: Spacing.xl,
  },
  featuredCardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  featuredCardBadges: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  featuredCardInfo: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  featuredCardTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
    marginBottom: Spacing.xs,
  },
  featuredCardDescription: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    opacity: 0.9,
    marginBottom: Spacing.md,
  },
  featuredCardStats: {
    flexDirection: 'row',
    gap: Spacing.lg,
  },

  // Search Bar
  searchContainer: {
    marginBottom: Spacing.xl,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    paddingVertical: Spacing.sm,
  },
  voiceButton: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  voiceButtonActive: {
    backgroundColor: Colors.error,
  },
  voiceTextContainer: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.md,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  filterToggle: {
    padding: Spacing.sm,
    marginLeft: Spacing.sm,
  },

  // Enhanced Filters
  filtersContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  filterSection: {
    marginBottom: Spacing.lg,
  },
  filterTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.muted,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.xs,
  },
  filterChipSelected: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  filterChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  filterChipTextSelected: {
    color: Colors.brandForeground,
  },
  clearFiltersButton: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  clearFiltersText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
  },

  // Dietary Preferences
  dietaryContainer: {
    flexDirection: 'row',
  },
  dietaryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.brand,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    gap: Spacing.xs,
  },
  dietaryChipSelected: {
    backgroundColor: Colors.brand,
  },
  dietaryChipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  dietaryChipTextSelected: {
    color: Colors.brandForeground,
  },

  // Generate Button
  generateContainer: {
    marginBottom: Spacing.xl,
  },
  generateButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    opacity: 0.6,
  },
  generateButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Recipe Cards
  recipeCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  recipeHeader: {
    marginBottom: Spacing.md,
  },
  recipeInfo: {
    marginBottom: Spacing.sm,
  },
  recipeTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  recipeDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  recipeStats: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  statText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginBottom: Spacing.md,
  },
  tag: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  tagText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  difficultyTag: {
    backgroundColor: Colors.muted,
  },
  difficultyText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.medium,
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ingredientsLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  viewButtonText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },

  // Suggestions Grid
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  suggestionCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  suggestionText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },

  // Filters Modal
  filtersModalContent: {
    padding: Spacing.xl,
  },
  modalSectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    marginTop: Spacing.lg,
  },
  modalFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    marginBottom: Spacing.xl,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.xl,
  },
  modalClearButton: {
    flex: 1,
  },
  modalApplyButton: {
    flex: 1,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
});

export default RecipesScreenModern;
