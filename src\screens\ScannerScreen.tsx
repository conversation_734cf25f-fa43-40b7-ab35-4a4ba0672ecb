import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import RadialGauge from '../components/RadialGauge';
import ApiService, { MealAnalysis } from '../services/ApiService';

const ScannerScreen: React.FC = () => {
  const [image, setImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<MealAnalysis | null>(null);
  const [loading, setLoading] = useState(false);

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const analyzeImage = async () => {
    if (!image) return;

    setLoading(true);

    try {
      // Convert image to base64
      const base64 = image.split(',')[1] || image;
      const result = await ApiService.scanMeal(base64);
      setAnalysis(result);
    } catch (error) {
      Alert.alert('Error', 'Failed to analyze the image. Please try again.');
      console.error('Analysis error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.backgroundAlt]}
        style={styles.backgroundGradient}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
            <Text style={styles.title}>Meal Scanner</Text>
            <Text style={styles.subtitle}>Snap a photo to analyze your food.</Text>
          </Animated.View>

          {/* Camera Section */}
          <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.cameraSection}>
            {image ? (
              <View style={styles.imageContainer}>
                <Image source={{ uri: image }} style={styles.selectedImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => {
                    setImage(null);
                    setAnalysis(null);
                  }}
                >
                  <Ionicons name="close-circle" size={24} color={Colors.error} />
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.placeholderContainer}>
                <Ionicons name="camera" size={64} color={Colors.textLight} />
                <Text style={styles.placeholderText}>No image selected</Text>
              </View>
            )}
          </Animated.View>

          {/* Action Buttons */}
          <Animated.View entering={FadeInUp.delay(400).duration(800)} style={styles.buttonContainer}>
            <TouchableOpacity style={styles.actionButton} onPress={takePhoto}>
              <LinearGradient
                colors={[Colors.primary, Colors.secondary]}
                style={styles.buttonGradient}
              >
                <Ionicons name="camera" size={24} color={Colors.white} />
                <Text style={styles.buttonText}>Take Photo</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={pickImage}>
              <LinearGradient
                colors={[Colors.secondary, Colors.primary]}
                style={styles.buttonGradient}
              >
                <Ionicons name="images" size={24} color={Colors.white} />
                <Text style={styles.buttonText}>Choose Image</Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Analyze Button */}
          {image && (
            <Animated.View entering={FadeInUp.delay(600).duration(800)} style={styles.analyzeContainer}>
              <TouchableOpacity
                style={[styles.analyzeButton, loading && styles.analyzeButtonDisabled]}
                onPress={analyzeImage}
                disabled={loading}
              >
                <LinearGradient
                  colors={loading ? [Colors.gray400, Colors.gray500] : [Colors.primary, Colors.secondary]}
                  style={styles.analyzeButtonGradient}
                >
                  {loading ? (
                    <Ionicons name="hourglass" size={24} color={Colors.white} />
                  ) : (
                    <Ionicons name="analytics" size={24} color={Colors.white} />
                  )}
                  <Text style={styles.analyzeButtonText}>
                    {loading ? 'Analyzing...' : 'Analyze Meal'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          )}

          {/* Analysis Results */}
          {analysis && (
            <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.resultsContainer}>
              <View style={styles.resultsCard}>
                <Text style={styles.resultsTitle}>{analysis.mealTitle}</Text>

                <View style={styles.nutritionGrid}>
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionValue}>{analysis.calories}</Text>
                    <Text style={styles.nutritionLabel}>Calories</Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <RadialGauge value={analysis.healthRating * 10} size={48} strokeWidth={4} />
                    <Text style={styles.nutritionLabel}>Health Score</Text>
                  </View>
                </View>

                <View style={styles.macrosGrid}>
                  <View style={styles.macroItem}>
                    <Text style={styles.macroValue}>{analysis.macros.protein}</Text>
                    <Text style={styles.macroLabel}>Protein</Text>
                  </View>
                  <View style={styles.macroItem}>
                    <Text style={styles.macroValue}>{analysis.macros.carbs}</Text>
                    <Text style={styles.macroLabel}>Carbs</Text>
                  </View>
                  <View style={styles.macroItem}>
                    <Text style={styles.macroValue}>{analysis.macros.fats}</Text>
                    <Text style={styles.macroLabel}>Fats</Text>
                  </View>
                </View>

                <View style={styles.itemsContainer}>
                  <Text style={styles.sectionTitle}>Items Identified</Text>
                  {analysis.itemsIdentified.map((item, index) => (
                    <View key={index} style={styles.itemRow}>
                      <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                      <Text style={styles.itemText}>{item}</Text>
                    </View>
                  ))}
                </View>

                <View style={styles.analysisContainer}>
                  <Text style={styles.sectionTitle}>Analysis</Text>

                  <View style={styles.analysisSection}>
                    <Text style={styles.analysisSubtitle}>Strengths</Text>
                    {analysis.analysis.strengths.map((strength, index) => (
                      <Text key={index} style={styles.analysisItem}>• {strength}</Text>
                    ))}
                  </View>

                  <View style={styles.analysisSection}>
                    <Text style={styles.analysisSubtitle}>Suggestions</Text>
                    {analysis.analysis.suggestions.map((suggestion, index) => (
                      <Text key={index} style={styles.analysisItem}>• {suggestion}</Text>
                    ))}
                  </View>
                </View>
              </View>
            </Animated.View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    textAlign: 'center',
  },
  cameraSection: {
    marginBottom: Spacing.lg,
  },
  imageContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  selectedImage: {
    width: 300,
    height: 300,
    borderRadius: BorderRadius.xl,
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.full,
  },
  placeholderContainer: {
    height: 300,
    backgroundColor: Colors.gray100,
    borderRadius: BorderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.gray200,
    borderStyle: 'dashed',
  },
  placeholderText: {
    fontSize: FontSizes.base,
    color: Colors.textLight,
    marginTop: Spacing.sm,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  buttonText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
  analyzeContainer: {
    marginBottom: Spacing.lg,
  },
  analyzeButton: {
    width: '100%',
  },
  analyzeButtonDisabled: {
    opacity: 0.7,
  },
  analyzeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  analyzeButtonText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
  resultsContainer: {
    marginBottom: Spacing.xxl,
  },
  resultsCard: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    shadowColor: Colors.gray900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  resultsTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  nutritionItem: {
    flex: 1,
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
  },
  nutritionValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.primary,
  },
  nutritionLabel: {
    fontSize: FontSizes.sm,
    color: Colors.textLight,
    marginTop: Spacing.xs,
  },
  macrosGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  macroItem: {
    flex: 1,
    backgroundColor: Colors.gray50,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
  },
  macroValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.text,
  },
  macroLabel: {
    fontSize: FontSizes.xs,
    color: Colors.textLight,
    marginTop: Spacing.xs,
  },
  itemsContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  itemText: {
    fontSize: FontSizes.base,
    color: Colors.text,
    marginLeft: Spacing.sm,
    flex: 1,
  },
  analysisContainer: {
    marginTop: Spacing.md,
  },
  analysisSection: {
    marginBottom: Spacing.md,
  },
  analysisSubtitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  analysisItem: {
    fontSize: FontSizes.sm,
    color: Colors.textLight,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
});

export default ScannerScreen;
