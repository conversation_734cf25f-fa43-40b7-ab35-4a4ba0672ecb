import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
  Dimensions,
  StatusBar,
  Pressable,
  PanGestureHandler,
  State,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  ZoomIn,
  SlideInUp,
  SlideInDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  Extrapolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernButton } from '../components/ModernButton';
import { ModernCard } from '../components/ModernCard';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import ApiService from '../services/ApiService';
import ScanHistoryService, { ScanResult, DetectedFood, DailyIntake } from '../services/ScanHistoryService';

const { width, height } = Dimensions.get('window');

interface EnhancedMealAnalysis {
  mealTitle: string;
  detectedFoods: DetectedFood[];
  totalNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
    calcium?: number;
    iron?: number;
    vitaminC?: number;
    vitaminA?: number;
  };
  healthRating: number;
  analysis: {
    strengths: string[];
    concerns: string[];
    suggestions: string[];
  };
  mealType: string;
  estimatedPrepTime?: string;
}

interface CameraControlsProps {
  onCapture: () => void;
  onGallery: () => void;
  onFlashToggle: () => void;
  onGridToggle: () => void;
  flashEnabled: boolean;
  gridLines: boolean;
  loading: boolean;
}

interface ScanOverlayProps {
  visible: boolean;
  progress: number;
  stage: string;
}

// Modern Camera Controls Component
const CameraControls: React.FC<CameraControlsProps> = ({
  onCapture,
  onGallery,
  onFlashToggle,
  onGridToggle,
  flashEnabled,
  gridLines,
  loading,
}) => {
  const captureScale = useSharedValue(1);
  const flashOpacity = useSharedValue(flashEnabled ? 1 : 0.5);
  const gridOpacity = useSharedValue(gridLines ? 1 : 0.5);

  const captureAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: captureScale.value }],
  }));

  const flashAnimatedStyle = useAnimatedStyle(() => ({
    opacity: flashOpacity.value,
  }));

  const gridAnimatedStyle = useAnimatedStyle(() => ({
    opacity: gridOpacity.value,
  }));

  const handleCapturePress = () => {
    captureScale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    onCapture();
  };

  const handleFlashToggle = () => {
    flashOpacity.value = withTiming(flashEnabled ? 0.5 : 1, { duration: 200 });
    onFlashToggle();
  };

  const handleGridToggle = () => {
    gridOpacity.value = withTiming(gridLines ? 0.5 : 1, { duration: 200 });
    onGridToggle();
  };

  return (
    <View style={styles.cameraControls}>
      {/* Top Controls */}
      <View style={styles.topControls}>
        <Animated.View style={flashAnimatedStyle}>
          <TouchableOpacity style={styles.controlButton} onPress={handleFlashToggle}>
            <Ionicons
              name={flashEnabled ? 'flash' : 'flash-off'}
              size={24}
              color={Colors.brandForeground}
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={gridAnimatedStyle}>
          <TouchableOpacity style={styles.controlButton} onPress={handleGridToggle}>
            <Ionicons
              name={gridLines ? 'grid' : 'grid-outline'}
              size={24}
              color={Colors.brandForeground}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Bottom Controls */}
      <View style={styles.bottomControls}>
        <TouchableOpacity style={styles.galleryButton} onPress={onGallery}>
          <Ionicons name="images" size={28} color={Colors.brandForeground} />
        </TouchableOpacity>

        <Animated.View style={[styles.captureButton, captureAnimatedStyle]}>
          <TouchableOpacity
            style={styles.captureButtonInner}
            onPress={handleCapturePress}
            disabled={loading}
          >
            {loading ? (
              <ModernLoading variant="spinner" size="md" color={Colors.brandForeground} />
            ) : (
              <View style={styles.captureButtonCenter} />
            )}
          </TouchableOpacity>
        </Animated.View>

        <TouchableOpacity style={styles.historyButton}>
          <Ionicons name="time" size={28} color={Colors.brandForeground} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Scan Overlay Component
const ScanOverlay: React.FC<ScanOverlayProps> = ({ visible, progress, stage }) => {
  const overlayOpacity = useSharedValue(visible ? 1 : 0);
  const progressWidth = useSharedValue(0);

  useEffect(() => {
    overlayOpacity.value = withTiming(visible ? 1 : 0, { duration: 300 });
    progressWidth.value = withTiming(progress, { duration: 500 });
  }, [visible, progress]);

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: overlayOpacity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  if (!visible) return null;

  return (
    <Animated.View style={[styles.scanOverlay, overlayAnimatedStyle]}>
      <BlurView intensity={80} style={styles.scanOverlayBlur}>
        <View style={styles.scanOverlayContent}>
          <Animated.View entering={ZoomIn.duration(600)}>
            <View style={styles.scanIcon}>
              <Ionicons name="scan" size={60} color={Colors.brand} />
            </View>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(200).duration(600)}>
            <Text style={styles.scanTitle}>Analyzing Your Food</Text>
            <Text style={styles.scanStage}>{stage}</Text>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, progressAnimatedStyle]} />
            </View>
            <Text style={styles.progressText}>{Math.round(progress)}%</Text>
          </Animated.View>
        </View>
      </BlurView>
    </Animated.View>
  );
};

const ScannerScreenEnhanced: React.FC = () => {
  const [image, setImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<EnhancedMealAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
  const [dailyIntake, setDailyIntake] = useState<DailyIntake | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [mealType, setMealType] = useState<'breakfast' | 'lunch' | 'dinner' | 'snack'>('lunch');
  const [notes, setNotes] = useState('');
  const [showSaveModal, setShowSaveModal] = useState(false);

  // Enhanced scanner states
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStage, setAnalysisStage] = useState('');
  const [detectionConfidence, setDetectionConfidence] = useState<{[key: string]: number}>({});
  const [nutritionComparison, setNutritionComparison] = useState<any>(null);
  const [foodSuggestions, setFoodSuggestions] = useState<string[]>([]);
  const [imageQuality, setImageQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>('good');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [gridLines, setGridLines] = useState(true);
  const [autoFocus, setAutoFocus] = useState(true);
  const [scanCount, setScanCount] = useState(0);
  const [lastScanTime, setLastScanTime] = useState<Date | null>(null);
  const [nutritionGoals, setNutritionGoals] = useState<any>(null);

  useEffect(() => {
    loadScanHistory();
    loadDailyIntake();
  }, []);



  const loadScanHistory = async () => {
    try {
      const history = await ScanHistoryService.getTodaysScans();
      setScanHistory(history);
    } catch (error) {
      console.error('Error loading scan history:', error);
    }
  };

  const loadDailyIntake = async () => {
    try {
      const intake = await ScanHistoryService.getDailyIntake();
      setDailyIntake(intake);
    } catch (error) {
      console.error('Error loading daily intake:', error);
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const analyzeImage = async () => {
    if (!image) return;

    setLoading(true);
    setAnalysisProgress(0);
    setAnalysisStage('Preparing image...');

    // Simulate progressive analysis stages
    const stages = [
      { stage: 'Analyzing image quality...', progress: 10 },
      { stage: 'Detecting food items...', progress: 30 },
      { stage: 'Identifying ingredients...', progress: 50 },
      { stage: 'Calculating nutrition...', progress: 70 },
      { stage: 'Generating insights...', progress: 90 },
      { stage: 'Finalizing results...', progress: 100 }
    ];

    // Progressive updates
    for (const { stage, progress } of stages) {
      setAnalysisStage(stage);
      setAnalysisProgress(progress);
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    try {
      const result = await ApiService.scanMeal(image);

      console.log('🔍 Raw API result:', JSON.stringify(result, null, 2));
      console.log('🔍 detectedFoods:', result.detectedFoods);
      console.log('🔍 totalNutrition:', result.totalNutrition);

      // Convert API response to enhanced format
      const enhancedAnalysis: EnhancedMealAnalysis = {
        mealTitle: result.mealTitle || 'Analyzed Meal',
        detectedFoods: result.detectedFoods || [
          {
            name: result.itemsIdentified?.[0] || 'Unknown Food',
            portion: '1 serving',
            confidence: 85,
            calories: result.totalNutrition?.calories || result.calories || 0,
            protein: result.totalNutrition?.protein || parseInt(result.macros?.protein) || 0,
            carbs: result.totalNutrition?.carbs || parseInt(result.macros?.carbs) || 0,
            fat: result.totalNutrition?.fat || parseInt(result.macros?.fats) || 0,
            fiber: result.totalNutrition?.fiber || 5,
            sugar: result.totalNutrition?.sugar || 3,
            sodium: result.totalNutrition?.sodium || 150,
            ingredients: result.detectedIngredients || ['Unknown ingredients'],
            cookingMethod: 'unknown',
            freshness: 'unknown'
          }
        ],
        totalNutrition: {
          calories: result.totalNutrition?.calories || result.calories || 0,
          protein: result.totalNutrition?.protein || parseInt(result.macros?.protein) || 0,
          carbs: result.totalNutrition?.carbs || parseInt(result.macros?.carbs) || 0,
          fat: result.totalNutrition?.fat || parseInt(result.macros?.fats) || 0,
          fiber: result.totalNutrition?.fiber || 8,
          sugar: result.totalNutrition?.sugar || 12,
          sodium: result.totalNutrition?.sodium || 680,
          calcium: result.totalNutrition?.calcium || 0,
          iron: result.totalNutrition?.iron || 0,
          vitaminC: result.totalNutrition?.vitaminC || 0,
          vitaminA: result.totalNutrition?.vitaminA || 0,
        },
        healthRating: result.healthRating || 7,
        analysis: {
          strengths: result.analysis?.strengths || ['Nutritious meal'],
          concerns: result.analysis?.concerns || [],
          suggestions: result.analysis?.suggestions || ['Enjoy your meal!'],
        },
        mealType: result.mealType || 'meal',
        estimatedPrepTime: result.estimatedPrepTime || '15 minutes',
      };
      
      setAnalysis(enhancedAnalysis);

      // Enhanced post-processing
      await processAnalysisResults(enhancedAnalysis);

      setShowSaveModal(true);
      setScanCount(prev => prev + 1);
      setLastScanTime(new Date());
    } catch (error) {
      console.error('Error analyzing meal:', error);
      Alert.alert('Analysis Failed', 'Unable to analyze the image. Please try again.');
    }
    setLoading(false);
    setAnalysisProgress(0);
    setAnalysisStage('');
  };

  // Enhanced analysis post-processing
  const processAnalysisResults = async (analysis: EnhancedMealAnalysis) => {
    // Calculate confidence scores for each detected food
    const confidenceScores: {[key: string]: number} = {};
    analysis.detectedFoods.forEach(food => {
      confidenceScores[food.name] = food.confidence || 85;
    });
    setDetectionConfidence(confidenceScores);

    // Generate nutrition comparison with daily goals
    if (dailyIntake) {
      const comparison = {
        caloriesProgress: (dailyIntake.calories + analysis.totalNutrition.calories) / 2000 * 100,
        proteinProgress: (dailyIntake.protein + analysis.totalNutrition.protein) / 150 * 100,
        carbsProgress: (dailyIntake.carbs + analysis.totalNutrition.carbs) / 300 * 100,
        fatProgress: (dailyIntake.fat + analysis.totalNutrition.fat) / 65 * 100,
      };
      setNutritionComparison(comparison);
    }

    // Generate smart food suggestions
    const suggestions = generateFoodSuggestions(analysis);
    setFoodSuggestions(suggestions);

    // Assess image quality
    const quality = assessImageQuality();
    setImageQuality(quality);
  };

  // Generate smart food suggestions based on analysis
  const generateFoodSuggestions = (analysis: EnhancedMealAnalysis): string[] => {
    const suggestions: string[] = [];

    if (analysis.totalNutrition.protein < 20) {
      suggestions.push('Add protein: Greek yogurt, eggs, or lean meat');
    }
    if (analysis.totalNutrition.fiber < 5) {
      suggestions.push('Boost fiber: Add vegetables or whole grains');
    }
    if (analysis.totalNutrition.sodium > 800) {
      suggestions.push('Reduce sodium: Use herbs and spices instead');
    }
    if (analysis.healthRating < 6) {
      suggestions.push('Improve balance: Add more colorful vegetables');
    }

    return suggestions.slice(0, 3);
  };

  // Assess image quality for better scanning tips
  const assessImageQuality = (): 'excellent' | 'good' | 'fair' | 'poor' => {
    // This would use actual image analysis in production
    const random = Math.random();
    if (random > 0.8) return 'excellent';
    if (random > 0.6) return 'good';
    if (random > 0.3) return 'fair';
    return 'poor';
  };

  const saveScanResult = async () => {
    if (!analysis || !image) return;

    try {
      const scanResult: ScanResult = {
        id: Date.now().toString(),
        timestamp: new Date(),
        imageUri: image,
        detectedFoods: analysis.detectedFoods,
        totalNutrition: analysis.totalNutrition,
        mealType,
        notes: notes.trim(),
      };

      await ScanHistoryService.saveScan(scanResult);
      await loadScanHistory();
      await loadDailyIntake();
      
      setShowSaveModal(false);
      setNotes('');
      Alert.alert('Saved!', 'Meal has been logged to your diary.');
    } catch (error) {
      console.error('Error saving scan:', error);
      Alert.alert('Save Failed', 'Unable to save the scan. Please try again.');
    }
  };



  const FoodCard: React.FC<{ food: DetectedFood; index: number }> = ({ food, index }) => (
    <Animated.View entering={ZoomIn.delay(index * 150).duration(800)} style={styles.foodCard}>
      <BlurView intensity={15} style={styles.foodCardBlur}>
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.95)', 'rgba(248, 250, 252, 0.9)']}
          style={styles.foodCardGradient}
        >
          <View style={styles.foodHeader}>
            <Text style={styles.foodName}>{food.name}</Text>
            <LinearGradient
              colors={
                food.confidence >= 90 ? [Colors.success, Colors.successDark] :
                food.confidence >= 70 ? [Colors.warning, Colors.warningDark] :
                [Colors.error, Colors.errorDark]
              }
              style={styles.confidenceBadge}
            >
              <Text style={styles.confidenceText}>{food.confidence}%</Text>
            </LinearGradient>
          </View>
          <Text style={styles.foodPortion}>{food.portion}</Text>

      {/* Ingredients */}
      {food.ingredients && food.ingredients.length > 0 && (
        <View style={styles.ingredientsContainer}>
          <Text style={styles.ingredientsLabel}>Ingredients:</Text>
          <Text style={styles.ingredientsText}>{food.ingredients.join(', ')}</Text>
        </View>
      )}

      {/* Cooking Method & Freshness */}
      <View style={styles.foodMeta}>
        {food.cookingMethod && food.cookingMethod !== 'unknown' && (
          <View style={styles.metaTag}>
            <Text style={styles.metaTagText}>{food.cookingMethod}</Text>
          </View>
        )}
        {food.freshness && food.freshness !== 'unknown' && (
          <View style={[styles.metaTag, styles.metaTagFreshness]}>
            <Text style={styles.metaTagText}>{food.freshness}</Text>
          </View>
        )}
      </View>

      <View style={styles.foodNutrition}>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{food.calories}</Text>
          <Text style={styles.nutritionLabel}>cal</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{food.protein}g</Text>
          <Text style={styles.nutritionLabel}>protein</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{food.carbs}g</Text>
          <Text style={styles.nutritionLabel}>carbs</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{food.fat}g</Text>
          <Text style={styles.nutritionLabel}>fat</Text>
        </View>
      </View>

          {/* Additional Nutrition */}
          <View style={styles.additionalNutrition}>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionValue}>{food.fiber || 0}g</Text>
              <Text style={styles.nutritionLabel}>fiber</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionValue}>{food.sugar || 0}g</Text>
              <Text style={styles.nutritionLabel}>sugar</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={styles.nutritionValue}>{food.sodium || 0}mg</Text>
              <Text style={styles.nutritionLabel}>sodium</Text>
            </View>
          </View>
        </LinearGradient>
      </BlurView>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Camera Viewfinder */}
      <View style={styles.cameraContainer}>
        {image ? (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.imagePreview}>
            <Image source={{ uri: image }} style={styles.previewImage} />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.imageOverlay}
            />
          </Animated.View>
        ) : (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.cameraViewfinder}>
            <LinearGradient
              colors={[Colors.brand, Colors.brandSecondary]}
              style={styles.viewfinderGradient}
            >
              <View style={styles.viewfinderContent}>
                <Animated.View entering={ZoomIn.delay(300).duration(800)}>
                  <View style={styles.scanFrame}>
                    <View style={styles.scanCorners}>
                      <View style={[styles.corner, styles.topLeft]} />
                      <View style={[styles.corner, styles.topRight]} />
                      <View style={[styles.corner, styles.bottomLeft]} />
                      <View style={[styles.corner, styles.bottomRight]} />
                    </View>

                    {gridLines && (
                      <View style={styles.gridOverlay}>
                        <View style={styles.gridLine} />
                        <View style={[styles.gridLine, styles.gridLineVertical]} />
                        <View style={[styles.gridLine, styles.gridLineHorizontal]} />
                        <View style={[styles.gridLine, styles.gridLineVertical2]} />
                      </View>
                    )}
                  </View>
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(500).duration(600)}>
                  <Text style={styles.scanInstruction}>Position food within the frame</Text>
                  <Text style={styles.scanSubInstruction}>Ensure good lighting for best results</Text>
                </Animated.View>
              </View>
            </LinearGradient>
          </Animated.View>
        )}

        {/* Camera Controls */}
        <CameraControls
          onCapture={takePhoto}
          onGallery={pickImage}
          onFlashToggle={() => setFlashEnabled(!flashEnabled)}
          onGridToggle={() => setGridLines(!gridLines)}
          flashEnabled={flashEnabled}
          gridLines={gridLines}
          loading={loading}
        />
      </View>

      {/* Scan Overlay */}
      <ScanOverlay
        visible={loading}
        progress={analysisProgress}
        stage={analysisStage}
      />

      {/* Results Panel */}
      {(analysis || image) && (
        <Animated.View entering={SlideInUp.duration(600)} style={styles.resultsPanel}>
          <ScrollView
            style={styles.resultsScroll}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.resultsContent}
          >
            {/* Quick Actions Header */}
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsTitle}>Scan Results</Text>
              <View style={styles.quickActions}>
                <TouchableOpacity
                  style={styles.quickActionButton}
                  onPress={() => setShowSaveModal(true)}
                >
                  <Ionicons name="bookmark" size={20} color={Colors.brand} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickActionButton}
                  onPress={() => setShowHistory(true)}
                >
                  <Ionicons name="time" size={20} color={Colors.brand} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickActionButton}
                  onPress={() => {
                    setImage(null);
                    setAnalysis(null);
                  }}
                >
                  <Ionicons name="close" size={20} color={Colors.mutedForeground} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Analysis Results */}
            {analysis && (
              <>
                {/* Meal Overview Card */}
                <ModernCard
                  title={analysis.mealTitle}
                  description={`${analysis.detectedFoods.length} items detected`}
                  variant="gradient"
                  style={styles.mealOverviewCard}
                >
                  <View style={styles.healthRating}>
                    <Text style={styles.healthRatingText}>Health Score</Text>
                    <Text style={styles.healthRatingValue}>{analysis.healthRating}/10</Text>
                  </View>
                </ModernCard>

                {/* Nutrition Summary */}
                <ModernCard
                  title="Nutrition Summary"
                  variant="glass"
                  style={styles.nutritionCard}
                >
                  <View style={styles.nutritionGrid}>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionValue}>{analysis.totalNutrition.calories}</Text>
                      <Text style={styles.nutritionLabel}>Calories</Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionValue}>{analysis.totalNutrition.protein}g</Text>
                      <Text style={styles.nutritionLabel}>Protein</Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionValue}>{analysis.totalNutrition.carbs}g</Text>
                      <Text style={styles.nutritionLabel}>Carbs</Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionValue}>{analysis.totalNutrition.fat}g</Text>
                      <Text style={styles.nutritionLabel}>Fat</Text>
                    </View>
                  </View>
                </ModernCard>

                {/* Detected Foods */}
                <ModernCard
                  title="Detected Foods"
                  variant="default"
                  style={styles.detectedFoodsCard}
                >
                  {analysis.detectedFoods.map((food, index) => (
                    <View key={index} style={styles.foodItem}>
                      <View style={styles.foodInfo}>
                        <Text style={styles.foodName}>{food.name}</Text>
                        <Text style={styles.foodDetails}>
                          {food.quantity} • {food.calories} cal
                        </Text>
                      </View>
                      <View style={styles.confidenceIndicator}>
                        <Text style={styles.confidenceText}>
                          {Math.round((detectionConfidence[food.name] || 0.8) * 100)}%
                        </Text>
                      </View>
                    </View>
                  ))}
                </ModernCard>

                {/* Analysis Insights */}
                <ModernCard
                  title="Health Insights"
                  variant="glass"
                  style={styles.insightsCard}
                >
                  {analysis.analysis.strengths.length > 0 && (
                    <View style={styles.insightSection}>
                      <Text style={styles.insightTitle}>✅ Strengths</Text>
                      {analysis.analysis.strengths.map((strength, index) => (
                        <Text key={index} style={styles.insightText}>• {strength}</Text>
                      ))}
                    </View>
                  )}

                  {analysis.analysis.suggestions.length > 0 && (
                    <View style={styles.insightSection}>
                      <Text style={styles.insightTitle}>💡 Suggestions</Text>
                      {analysis.analysis.suggestions.map((suggestion, index) => (
                        <Text key={index} style={styles.insightText}>• {suggestion}</Text>
                      ))}
                    </View>
                  )}
                </ModernCard>

                {/* Save Button */}
                <ModernButton
                  title="Save to Meal Diary"
                  onPress={() => setShowSaveModal(true)}
                  variant="primary"
                  size="lg"
                  icon="bookmark"
                  fullWidth
                  style={styles.saveButton}
                />
              </>
            )}
          </ScrollView>
        </Animated.View>
      )}

      {/* Modern Save Modal */}
      <ModernModal
        visible={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        title="Save to Meal Diary"
        variant="center"
        size="md"
      >
        <View style={styles.saveModalContent}>
          <Text style={styles.modalLabel}>Meal Type</Text>
          <View style={styles.mealTypeGrid}>
            {(['breakfast', 'lunch', 'dinner', 'snack'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.mealTypeButton,
                  mealType === type && styles.mealTypeButtonSelected
                ]}
                onPress={() => setMealType(type)}
              >
                <Ionicons
                  name={getMealTypeIcon(type)}
                  size={24}
                  color={mealType === type ? Colors.brandForeground : Colors.brand}
                />
                <Text style={[
                  styles.mealTypeText,
                  mealType === type && styles.mealTypeTextSelected
                ]}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <Text style={styles.modalLabel}>Notes (Optional)</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add notes about this meal..."
            placeholderTextColor={Colors.mutedForeground}
            value={notes}
            onChangeText={setNotes}
            multiline
            maxLength={200}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Cancel"
              onPress={() => setShowSaveModal(false)}
              variant="outline"
              size="md"
              style={styles.modalCancelButton}
            />
            <ModernButton
              title="Save Meal"
              onPress={saveScanResult}
              variant="primary"
              size="md"
              icon="bookmark"
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>

      {/* History Modal */}
      <ModernModal
        visible={showHistory}
        onClose={() => setShowHistory(false)}
        title="Scan History"
        variant="fullscreen"
      >
        <ScrollView
          style={styles.historyScroll}
          showsVerticalScrollIndicator={false}
        >
          {scanHistory.map((scan, index) => (
            <ModernCard
              key={scan.id}
              title={scan.mealTitle}
              description={`${scan.detectedFoods.length} items • ${scan.totalNutrition.calories} cal`}
              variant="default"
              animationDelay={index * 100}
              style={styles.historyCard}
            >
              <View style={styles.historyCardActions}>
                <Text style={styles.historyTime}>
                  {new Date(scan.timestamp).toLocaleTimeString()}
                </Text>
                <TouchableOpacity
                  style={styles.historyDeleteButton}
                  onPress={() => deleteHistoryItem(scan.id)}
                >
                  <Ionicons name="trash" size={16} color={Colors.error} />
                </TouchableOpacity>
              </View>
            </ModernCard>
          ))}
        </ScrollView>
      </ModernModal>
    </View>
  );
};

// Helper function for meal type icons
const getMealTypeIcon = (type: string): keyof typeof Ionicons.glyphMap => {
  switch (type) {
    case 'breakfast': return 'sunny';
    case 'lunch': return 'restaurant';
    case 'dinner': return 'moon';
    case 'snack': return 'fast-food';
    default: return 'restaurant';
  }
};

// Helper function to delete history item
const deleteHistoryItem = async (scanId: string) => {
  Alert.alert(
    'Delete Scan',
    'Are you sure you want to delete this scan?',
    [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          await ScanHistoryService.deleteScan(scanId);
          // Reload history would be handled by the parent component
        },
      },
    ]
  );
};

            {/* Image Quality Indicator */}
            <View style={styles.imageQualityIndicator}>
              <Ionicons
                name={imageQuality === 'excellent' ? 'checkmark-circle' :
                     imageQuality === 'good' ? 'checkmark-circle-outline' :
                     imageQuality === 'fair' ? 'warning-outline' : 'close-circle-outline'}
                size={16}
                color={imageQuality === 'excellent' || imageQuality === 'good' ? Colors.success :
                       imageQuality === 'fair' ? Colors.warning : Colors.error}
              />
              <Text style={styles.imageQualityText}>
                Image quality: {imageQuality}
              </Text>
            </View>
          </Animated.View>
        )}

        {/* Enhanced Analysis Results */}
        {analysis && (
          <Animated.View entering={SlideInUp.delay(1000).duration(1000)} style={styles.resultsSection}>
            <Animated.View entering={FadeInLeft.delay(1200).duration(800)}>
              <Text style={styles.resultsTitle}>{analysis.mealTitle}</Text>
            </Animated.View>

            {/* Enhanced Health Score */}
            <Animated.View entering={ZoomIn.delay(1400).duration(800)} style={styles.healthScoreCard}>
              <BlurView intensity={20} style={styles.healthScoreBlur}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.95)', 'rgba(248, 250, 252, 0.9)']}
                  style={styles.healthScoreGradient}
                >
                  <View style={styles.healthScoreHeader}>
                    <Text style={styles.healthScoreTitle}>Health Score</Text>
                    <LinearGradient
                      colors={
                        analysis.healthRating >= 8 ? [Colors.success, Colors.successDark] :
                        analysis.healthRating >= 6 ? [Colors.warning, Colors.warningDark] :
                        [Colors.error, Colors.errorDark]
                      }
                      style={styles.healthScoreBadge}
                    >
                      <Text style={styles.healthScoreValue}>{analysis.healthRating}/10</Text>
                    </LinearGradient>
                  </View>
                  <View style={styles.healthScoreBar}>
                    <LinearGradient
                      colors={
                        analysis.healthRating >= 8 ? [Colors.success, Colors.successDark] :
                        analysis.healthRating >= 6 ? [Colors.warning, Colors.warningDark] :
                        [Colors.error, Colors.errorDark]
                      }
                      style={[
                        styles.healthScoreFill,
                        { width: `${analysis.healthRating * 10}%` }
                      ]}
                    />
                  </View>
                </LinearGradient>
              </BlurView>
            </Animated.View>

            {/* Detected Foods */}
            <View style={styles.detectedFoodsSection}>
              <Text style={styles.sectionTitle}>Detected Foods</Text>
              {analysis.detectedFoods.map((food, index) => (
                <FoodCard key={index} food={food} index={index} />
              ))}
            </View>

            {/* Total Nutrition */}
            <View style={styles.totalNutritionCard}>
              <Text style={styles.cardTitle}>Total Nutrition</Text>
              <View style={styles.nutritionGrid}>
                <View style={styles.nutritionGridItem}>
                  <Text style={styles.nutritionGridValue}>{analysis.totalNutrition.calories}</Text>
                  <Text style={styles.nutritionGridLabel}>Calories</Text>
                </View>
                <View style={styles.nutritionGridItem}>
                  <Text style={styles.nutritionGridValue}>{analysis.totalNutrition.protein}g</Text>
                  <Text style={styles.nutritionGridLabel}>Protein</Text>
                </View>
                <View style={styles.nutritionGridItem}>
                  <Text style={styles.nutritionGridValue}>{analysis.totalNutrition.carbs}g</Text>
                  <Text style={styles.nutritionGridLabel}>Carbs</Text>
                </View>
                <View style={styles.nutritionGridItem}>
                  <Text style={styles.nutritionGridValue}>{analysis.totalNutrition.fat}g</Text>
                  <Text style={styles.nutritionGridLabel}>Fat</Text>
                </View>
              </View>
            </View>

            {/* Analysis Insights */}
            <View style={styles.analysisCard}>
              <Text style={styles.cardTitle}>AI Insights</Text>
              
              {analysis.analysis.strengths.length > 0 && (
                <View style={styles.insightSection}>
                  <Text style={styles.insightTitle}>✅ Strengths</Text>
                  {analysis.analysis.strengths.map((strength, index) => (
                    <Text key={index} style={styles.insightText}>• {strength}</Text>
                  ))}
                </View>
              )}
              
              {analysis.analysis.concerns.length > 0 && (
                <View style={styles.insightSection}>
                  <Text style={styles.insightTitle}>⚠️ Concerns</Text>
                  {analysis.analysis.concerns.map((concern, index) => (
                    <Text key={index} style={styles.insightText}>• {concern}</Text>
                  ))}
                </View>
              )}
              
              {analysis.analysis.suggestions.length > 0 && (
                <View style={styles.insightSection}>
                  <Text style={styles.insightTitle}>💡 Suggestions</Text>
                  {analysis.analysis.suggestions.map((suggestion, index) => (
                    <Text key={index} style={styles.insightText}>• {suggestion}</Text>
                  ))}
                </View>
              )}
            </View>

            {/* Enhanced Features */}
            {/* Detection Confidence */}
            {Object.keys(detectionConfidence).length > 0 && (
              <View style={styles.confidenceCard}>
                <Text style={styles.cardTitle}>Detection Confidence</Text>
                {Object.entries(detectionConfidence).map(([food, confidence]) => (
                  <View key={food} style={styles.confidenceItem}>
                    <Text style={styles.confidenceFood}>{food}</Text>
                    <View style={styles.confidenceBarContainer}>
                      <View style={[styles.confidenceBar, { width: `${confidence}%` }]} />
                    </View>
                    <Text style={styles.confidenceValue}>{confidence}%</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Nutrition Comparison */}
            {nutritionComparison && (
              <View style={styles.comparisonCard}>
                <Text style={styles.cardTitle}>Daily Progress Impact</Text>
                <View style={styles.comparisonGrid}>
                  <View style={styles.comparisonItem}>
                    <Text style={styles.comparisonLabel}>Calories</Text>
                    <View style={styles.comparisonBarContainer}>
                      <View style={[styles.comparisonBar, { width: `${Math.min(nutritionComparison.caloriesProgress, 100)}%` }]} />
                    </View>
                    <Text style={styles.comparisonValue}>{Math.round(nutritionComparison.caloriesProgress)}%</Text>
                  </View>
                  <View style={styles.comparisonItem}>
                    <Text style={styles.comparisonLabel}>Protein</Text>
                    <View style={styles.comparisonBarContainer}>
                      <View style={[styles.comparisonBar, { width: `${Math.min(nutritionComparison.proteinProgress, 100)}%` }]} />
                    </View>
                    <Text style={styles.comparisonValue}>{Math.round(nutritionComparison.proteinProgress)}%</Text>
                  </View>
                </View>
              </View>
            )}

            {/* Smart Suggestions */}
            {foodSuggestions.length > 0 && (
              <View style={styles.suggestionsCard}>
                <Text style={styles.cardTitle}>Smart Suggestions</Text>
                {foodSuggestions.map((suggestion, index) => (
                  <View key={index} style={styles.suggestionItem}>
                    <Ionicons name="bulb-outline" size={16} color={Colors.brand} />
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Scan Statistics */}
            <View style={styles.statsCard}>
              <Text style={styles.cardTitle}>Scan Statistics</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{scanCount}</Text>
                  <Text style={styles.statLabel}>Today's Scans</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{scanHistory.length}</Text>
                  <Text style={styles.statLabel}>Total Scans</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {lastScanTime ? lastScanTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '--:--'}
                  </Text>
                  <Text style={styles.statLabel}>Last Scan</Text>
                </View>
              </View>
            </View>
          </Animated.View>
        )}

        {/* Scan History Toggle */}
        {scanHistory.length > 0 && (
          <Animated.View entering={FadeInUp.delay(1200).duration(800)} style={styles.historyToggle}>
            <TouchableOpacity
              style={styles.historyButton}
              onPress={() => setShowHistory(!showHistory)}
            >
              <Text style={styles.historyButtonText}>
                Today's Scans ({scanHistory.length})
              </Text>
              <Ionicons 
                name={showHistory ? "chevron-up" : "chevron-down"} 
                size={20} 
                color={Colors.brand} 
              />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Scan History */}
        {showHistory && scanHistory.length > 0 && (
          <Animated.View entering={FadeInUp.duration(600)} style={styles.historySection}>
            {scanHistory.map((scan, index) => (
              <View key={scan.id} style={styles.historyItem}>
                <Image source={{ uri: scan.imageUri }} style={styles.historyImage} />
                <View style={styles.historyContent}>
                  <Text style={styles.historyTitle}>
                    {scan.detectedFoods[0]?.name || 'Meal'}
                  </Text>
                  <Text style={styles.historyTime}>
                    {scan.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                  <Text style={styles.historyCalories}>
                    {scan.totalNutrition.calories} calories
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.historyDeleteButton}
                  onPress={() => {
                    Alert.alert(
                      'Delete Scan',
                      'Are you sure you want to delete this scan?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Delete',
                          style: 'destructive',
                          onPress: async () => {
                            await ScanHistoryService.deleteScan(scan.id);
                            loadScanHistory();
                            loadDailyIntake();
                          },
                        },
                      ]
                    );
                  }}
                >
                  <Ionicons name="trash" size={16} color={Colors.error} />
                </TouchableOpacity>
              </View>
            ))}
          </Animated.View>
        )}
      </ScrollView>

      {/* Save Modal */}
      <Modal
        visible={showSaveModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSaveModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save to Meal Diary</Text>
            
            <Text style={styles.modalLabel}>Meal Type</Text>
            <View style={styles.mealTypeContainer}>
              {(['breakfast', 'lunch', 'dinner', 'snack'] as const).map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.mealTypeButton,
                    mealType === type && styles.mealTypeButtonSelected
                  ]}
                  onPress={() => setMealType(type)}
                >
                  <Text style={[
                    styles.mealTypeText,
                    mealType === type && styles.mealTypeTextSelected
                  ]}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.modalLabel}>Notes (Optional)</Text>
            <TextInput
              style={styles.notesInput}
              placeholder="Add notes about this meal..."
              placeholderTextColor={Colors.mutedForeground}
              value={notes}
              onChangeText={setNotes}
              multiline
              maxLength={200}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowSaveModal(false)}
              >
                <Text style={styles.modalCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={saveScanResult}
              >
                <Text style={styles.modalSaveText}>Save Meal</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Base Container
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundTertiary,
  },

  // Camera Container
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },

  // Camera Viewfinder
  cameraViewfinder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewfinderGradient: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewfinderContent: {
    alignItems: 'center',
    padding: Spacing.xxxl,
  },

  // Scan Frame
  scanFrame: {
    width: width * 0.8,
    height: width * 0.8,
    position: 'relative',
    marginBottom: Spacing.xxxl,
  },
  scanCorners: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: Colors.brandForeground,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: BorderRadius.md,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: BorderRadius.md,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: BorderRadius.md,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: BorderRadius.md,
  },

  // Grid Overlay
  gridOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: Colors.brandForeground,
    opacity: 0.3,
  },
  gridLineVertical: {
    width: 1,
    height: '100%',
    left: '33.33%',
  },
  gridLineVertical2: {
    width: 1,
    height: '100%',
    left: '66.66%',
  },
  gridLineHorizontal: {
    height: 1,
    width: '100%',
    top: '33.33%',
  },

  // Scan Instructions
  scanInstruction: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.brandForeground,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  scanSubInstruction: {
    fontSize: FontSizes.base,
    color: Colors.brandForeground,
    opacity: 0.8,
    textAlign: 'center',
  },

  // Daily Progress
  dailyProgress: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  progressTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  progressGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressItem: {
    alignItems: 'center',
    flex: 1,
  },
  progressValue: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  progressLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  progressPercent: {
    fontSize: FontSizes.xs,
    color: Colors.brandSecondary,
    marginTop: Spacing.xs,
  },

  // Image Preview
  imagePreview: {
    flex: 1,
    position: 'relative',
  },
  previewImage: {
    flex: 1,
    width: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 200,
  },

  // Camera Controls
  cameraControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: Spacing.xl,
    paddingTop: 60, // Account for status bar
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 40,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },

  // Capture Button
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.brandForeground,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.lg,
  },
  captureButtonInner: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonCenter: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.brandForeground,
  },

  // Gallery and History Buttons
  galleryButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Modern Camera Section
  cameraSection: {
    marginBottom: Spacing.xxl,
  },
  cameraCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  imageContainer: {
    position: 'relative',
  },
  selectedImage: {
    width: '100%',
    height: 280,
  },
  removeButton: {
    position: 'absolute',
    top: Spacing.lg,
    right: Spacing.lg,
  },
  removeButtonBackground: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  placeholderContainer: {
    height: 280,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  placeholderIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  placeholderText: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  placeholderSubtext: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Modern Button Container
  buttonContainer: {
    gap: Spacing.lg,
    marginBottom: Spacing.xxl,
  },

  // Modern Progress Styles
  progressContainer: {
    marginBottom: Spacing.xxl,
  },
  progressCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.xl,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
  },
  progressHeaderText: {
    flex: 1,
  },
  progressTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  progressSubtitle: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontWeight: FontWeights.normal,
  },
  progressPercentageContainer: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginLeft: Spacing.lg,
  },
  progressPercentage: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.brand,
  },
  progressBarContainer: {
    marginBottom: Spacing.lg,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: 4,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  secondaryButtonText: {
    color: Colors.brand,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },

  // Analyze Button
  analyzeContainer: {
    marginBottom: Spacing.xl,
  },
  analyzeButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  analyzeButtonDisabled: {
    opacity: 0.6,
  },
  analyzeButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Scan Overlay
  scanOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  scanOverlayBlur: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanOverlayContent: {
    alignItems: 'center',
    padding: Spacing.xxxl,
  },
  scanIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xxl,
    ...Shadows.lg,
  },
  scanTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  scanStage: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.xxl,
  },
  progressContainer: {
    width: '80%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    marginBottom: Spacing.md,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },
  progressText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
  },

  // Results Panel
  resultsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    maxHeight: height * 0.7,
    backgroundColor: Colors.background,
    borderTopLeftRadius: BorderRadius.xxl,
    borderTopRightRadius: BorderRadius.xxl,
    ...Shadows.xl,
  },
  resultsScroll: {
    flex: 1,
  },
  resultsContent: {
    padding: Spacing.xl,
    paddingBottom: 120, // Space for tab bar
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    paddingBottom: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  resultsTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  quickActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Meal Overview Card
  mealOverviewCard: {
    marginBottom: Spacing.lg,
  },
  healthRating: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  healthRatingText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brandForeground,
  },
  healthRatingValue: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },

  // Nutrition Card
  nutritionCard: {
    marginBottom: Spacing.lg,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: Spacing.lg,
  },
  nutritionItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  nutritionValue: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
    marginBottom: Spacing.xs,
  },
  nutritionLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Detected Foods Card
  detectedFoodsCard: {
    marginBottom: Spacing.lg,
  },
  foodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  foodInfo: {
    flex: 1,
  },
  foodName: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  foodDetails: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },
  confidenceIndicator: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  confidenceText: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Insights Card
  insightsCard: {
    marginBottom: Spacing.lg,
  },
  insightSection: {
    marginBottom: Spacing.lg,
  },
  insightTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  insightText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 20,
    marginBottom: Spacing.xs,
  },

  // Save Button
  saveButton: {
    marginTop: Spacing.lg,
  },

  // Health Score Card
  healthScoreCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  healthScoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  healthScoreTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  healthScoreBadge: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  healthScoreValue: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  healthScoreBar: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  healthScoreFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },

  // Detected Foods
  detectedFoodsSection: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  foodCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  foodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  foodName: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    flex: 1,
  },
  confidenceBadge: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  confidenceText: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  foodPortion: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  foodNutrition: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  nutritionLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Enhanced Food Card Styles
  ingredientsContainer: {
    marginBottom: Spacing.sm,
  },
  ingredientsLabel: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
    marginBottom: Spacing.xs,
  },
  ingredientsText: {
    fontSize: FontSizes.xs,
    color: Colors.foreground,
    lineHeight: 16,
  },
  foodMeta: {
    flexDirection: 'row',
    gap: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  metaTag: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  metaTagFreshness: {
    backgroundColor: Colors.success + '20',
  },
  metaTagText: {
    fontSize: FontSizes.xs,
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  additionalNutrition: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },

  // Total Nutrition Card
  totalNutritionCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  cardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  nutritionGridItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
  },
  nutritionGridValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  nutritionGridLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Analysis Card
  analysisCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  insightSection: {
    marginBottom: Spacing.md,
  },
  insightTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  insightText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 18,
    marginBottom: Spacing.xs,
  },

  // History
  historyToggle: {
    marginBottom: Spacing.lg,
  },
  historyButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  historyButtonText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  historySection: {
    marginBottom: Spacing.xl,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  historyImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.md,
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  historyTime: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  historyCalories: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    marginTop: Spacing.xs,
  },
  historyDeleteButton: {
    padding: Spacing.sm,
  },

  // Modern Analysis Steps
  analysisSteps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  analysisStep: {
    alignItems: 'center',
    flex: 1,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  stepCompleted: {
    backgroundColor: Colors.brand,
  },
  stepActive: {
    backgroundColor: Colors.brand,
  },
  stepPending: {
    backgroundColor: Colors.muted,
  },
  stepNumber: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.semibold,
    color: Colors.mutedForeground,
  },
  stepNumberActive: {
    color: Colors.brandForeground,
  },
  stepLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    textAlign: 'center',
    fontWeight: FontWeights.medium,
  },
  stepLabelActive: {
    color: Colors.foreground,
    fontWeight: FontWeights.semibold,
  },

  // Modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: BorderRadius.xl,
    borderTopRightRadius: BorderRadius.xl,
    padding: Spacing.xl,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  modalLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  mealTypeContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  mealTypeButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  mealTypeButtonSelected: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  mealTypeText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  mealTypeTextSelected: {
    color: Colors.brandForeground,
  },
  notesInput: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: Spacing.lg,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  modalSaveButton: {
    flex: 1,
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  modalSaveText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.brandForeground,
  },

  // Enhanced Scanner Styles
  progressContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginHorizontal: Spacing.lg,
    marginVertical: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  progressTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  progressPercentage: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: 4,
    marginBottom: Spacing.md,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: 4,
  },
  progressStage: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    fontStyle: 'italic',
  },
  analysisSteps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  analysisStep: {
    alignItems: 'center',
    flex: 1,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  stepCompleted: {
    backgroundColor: Colors.success,
  },
  stepActive: {
    backgroundColor: Colors.brand,
  },
  stepPending: {
    backgroundColor: Colors.muted,
    borderWidth: 2,
    borderColor: Colors.border,
  },
  stepNumber: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  stepLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  stepLabelActive: {
    color: Colors.brand,
    fontWeight: FontWeights.medium,
  },
  imageQualityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.sm,
    gap: Spacing.xs,
  },
  imageQualityText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Enhanced Results Styles
  confidenceCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginTop: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  confidenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    gap: Spacing.md,
  },
  confidenceFood: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    flex: 1,
  },
  confidenceBarContainer: {
    flex: 2,
    height: 6,
    backgroundColor: Colors.muted,
    borderRadius: 3,
    overflow: 'hidden',
  },
  confidenceBar: {
    height: '100%',
    backgroundColor: Colors.success,
    borderRadius: 3,
  },
  confidenceValue: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.success,
    minWidth: 40,
    textAlign: 'right',
  },

  // Comparison Styles
  comparisonCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginTop: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  comparisonGrid: {
    gap: Spacing.md,
  },
  comparisonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  comparisonLabel: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    flex: 1,
  },
  comparisonBarContainer: {
    flex: 2,
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: 4,
    overflow: 'hidden',
  },
  comparisonBar: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: 4,
  },
  comparisonValue: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
    minWidth: 40,
    textAlign: 'right',
  },

  // Suggestions Styles
  suggestionsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginTop: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  suggestionText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    flex: 1,
  },

  // Stats Styles
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginTop: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  statLabel: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
});

export default ScannerScreenEnhanced;
