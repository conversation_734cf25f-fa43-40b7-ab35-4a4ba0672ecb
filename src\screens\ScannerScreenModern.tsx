import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import ApiService from '../services/ApiService';

interface MealAnalysis {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  foodItems: string[];
  healthScore: number;
  recommendations: string[];
}

const ScannerScreenModern: React.FC = () => {
  const [image, setImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<MealAnalysis | null>(null);
  const [loading, setLoading] = useState(false);

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const analyzeImage = async () => {
    if (!image) return;
    
    setLoading(true);
    try {
      const result = await ApiService.scanMeal(image);
      // Convert the API response to match our interface
      setAnalysis({
        calories: result.calories,
        protein: parseInt(result.macros.protein),
        carbs: parseInt(result.macros.carbs),
        fat: parseInt(result.macros.fats),
        fiber: 8, // Default value
        sugar: 12, // Default value
        sodium: 680, // Default value
        foodItems: result.itemsIdentified,
        healthScore: result.healthRating * 10, // Convert 1-10 to 1-100
        recommendations: result.analysis.suggestions
      });
    } catch (error) {
      console.error('Error analyzing meal:', error);
      // Fallback to mock data for demo
      setAnalysis({
        calories: 450,
        protein: 25,
        carbs: 35,
        fat: 18,
        fiber: 8,
        sugar: 12,
        sodium: 680,
        foodItems: ['Grilled Chicken Breast', 'Mixed Vegetables', 'Brown Rice'],
        healthScore: 85,
        recommendations: [
          'Great protein content!',
          'Consider reducing sodium',
          'Add more leafy greens'
        ]
      });
    }
    setLoading(false);
  };

  const NutrientCard: React.FC<{ label: string; value: string | number; unit: string; icon: string }> = ({ 
    label, 
    value, 
    unit, 
    icon 
  }) => (
    <View style={styles.nutrientCard}>
      <View style={styles.nutrientIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.brand} />
      </View>
      <Text style={styles.nutrientValue}>{value}{unit}</Text>
      <Text style={styles.nutrientLabel}>{label}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Stunning Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Text style={styles.title}>AI Food Scanner</Text>
          <Text style={styles.subtitle}>Discover nutrition insights instantly</Text>
        </Animated.View>

        {/* Beautiful Camera Section */}
        <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.cameraSection}>
          {image ? (
            <View style={styles.imageContainer}>
              <Image source={{ uri: image }} style={styles.selectedImage} />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => {
                  setImage(null);
                  setAnalysis(null);
                }}
              >
                <View style={styles.removeButtonInner}>
                  <Ionicons name="close" size={18} color="white" />
                </View>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <View style={styles.placeholderIcon}>
                <Ionicons name="camera" size={56} color={Colors.brand} />
              </View>
              <Text style={styles.placeholderText}>Ready to scan</Text>
              <Text style={styles.placeholderSubtext}>Take a photo or choose from your gallery</Text>
            </View>
          )}
        </Animated.View>

        {/* Beautiful Action Buttons */}
        <Animated.View entering={FadeInUp.delay(500).duration(800)} style={styles.buttonContainer}>
          <TouchableOpacity style={styles.primaryButton} onPress={takePhoto}>
            <View style={styles.buttonIcon}>
              <Ionicons name="camera" size={22} color="white" />
            </View>
            <Text style={styles.primaryButtonText}>Take Photo</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={pickImage}>
            <View style={styles.buttonIconSecondary}>
              <Ionicons name="images" size={22} color={Colors.brand} />
            </View>
            <Text style={styles.secondaryButtonText}>Choose Image</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Stunning Analyze Button */}
        {image && (
          <Animated.View entering={FadeInUp.delay(700).duration(800)} style={styles.analyzeContainer}>
            <TouchableOpacity
              style={[styles.analyzeButton, loading && styles.analyzeButtonDisabled]}
              onPress={analyzeImage}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <View style={styles.analyzeIcon}>
                  <Ionicons name="scan" size={24} color="white" />
                </View>
              )}
              <Text style={styles.analyzeButtonText}>
                {loading ? 'Analyzing with AI...' : 'Analyze with AI'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Analysis Results */}
        {analysis && (
          <Animated.View entering={FadeInUp.delay(800).duration(800)} style={styles.resultsSection}>
            <Text style={styles.resultsTitle}>Analysis Results</Text>
            
            {/* Health Score */}
            <View style={styles.healthScoreCard}>
              <View style={styles.healthScoreHeader}>
                <Text style={styles.healthScoreTitle}>Health Score</Text>
                <View style={styles.healthScoreBadge}>
                  <Text style={styles.healthScoreValue}>{analysis.healthScore}/100</Text>
                </View>
              </View>
              <View style={styles.healthScoreBar}>
                <View 
                  style={[
                    styles.healthScoreFill, 
                    { width: `${analysis.healthScore}%` }
                  ]} 
                />
              </View>
            </View>

            {/* Nutrients Grid */}
            <View style={styles.nutrientsGrid}>
              <NutrientCard label="Calories" value={analysis.calories} unit="" icon="flame" />
              <NutrientCard label="Protein" value={analysis.protein} unit="g" icon="fitness" />
              <NutrientCard label="Carbs" value={analysis.carbs} unit="g" icon="leaf" />
              <NutrientCard label="Fat" value={analysis.fat} unit="g" icon="water" />
            </View>

            {/* Food Items */}
            <View style={styles.foodItemsCard}>
              <Text style={styles.cardTitle}>Detected Foods</Text>
              {analysis.foodItems.map((item, index) => (
                <View key={index} style={styles.foodItem}>
                  <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                  <Text style={styles.foodItemText}>{item}</Text>
                </View>
              ))}
            </View>

            {/* Recommendations */}
            <View style={styles.recommendationsCard}>
              <Text style={styles.cardTitle}>Recommendations</Text>
              {analysis.recommendations.map((rec, index) => (
                <View key={index} style={styles.recommendation}>
                  <Ionicons name="bulb" size={16} color={Colors.brandSecondary} />
                  <Text style={styles.recommendationText}>{rec}</Text>
                </View>
              ))}
            </View>
          </Animated.View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 32,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },

  // Beautiful Camera Section
  cameraSection: {
    marginBottom: 32,
  },
  imageContainer: {
    position: 'relative',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
  },
  selectedImage: {
    width: '100%',
    height: 280,
    borderRadius: 16,
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  removeButtonInner: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderContainer: {
    height: 280,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    marginBottom: Spacing.md,
  },
  placeholderText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  placeholderSubtext: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Buttons
  buttonContainer: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginBottom: Spacing.xl,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#22C55E',
    borderRadius: 20,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(34, 197, 94, 0.2)',
  },
  primaryButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.brand,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  secondaryButtonText: {
    color: Colors.brand,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
  },

  // Analyze Button
  analyzeContainer: {
    marginBottom: Spacing.xl,
  },
  analyzeButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  analyzeButtonDisabled: {
    opacity: 0.6,
  },
  analyzeButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Results Section
  resultsSection: {
    marginBottom: Spacing.xl,
  },
  resultsTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },

  // Health Score Card
  healthScoreCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  healthScoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  healthScoreTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
  },
  healthScoreBadge: {
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  healthScoreValue: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.bold,
    color: Colors.brand,
  },
  healthScoreBar: {
    height: 8,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  healthScoreFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },

  // Nutrients Grid
  nutrientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },
  nutrientCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  nutrientIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  nutrientValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  nutrientLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Cards
  foodItemsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  recommendationsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  cardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  foodItemText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
  },
  recommendation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  recommendationText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },

  // Missing Beautiful Button Styles
  buttonIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonIconSecondary: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    borderRadius: 8,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  analyzeIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
});

export default ScannerScreenModern;
