import { userProfile } from '../constants/UserData';

// Use your existing Next.js app's API - no separate backend needed!
const API_BASE_URL = 'http://localhost:3001'; // Your Next.js app URL

// Direct Gemini API configuration for fallback
const GEMINI_API_KEY = 'AIzaSyA-EVzCpQwPpKCnsUe2CRqdWIo8LTIKVnM';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

// Helper function to clean JSON response
function cleanJsonString(rawString: string): string {
  return rawString.replace(/```json\s*([\s\S]*?)\s*```/, "$1").trim();
}

export interface MealAnalysis {
  mealTitle: string;
  // New enhanced structure
  detectedFoods?: Array<{
    name: string;
    portion: string;
    confidence: number;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar?: number;
    sodium?: number;
    ingredients?: string[];
    cookingMethod?: string;
    freshness?: string;
  }>;
  totalNutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
    calcium?: number;
    iron?: number;
    vitaminC?: number;
    vitaminA?: number;
  };
  detectedIngredients?: string[];
  estimatedWeight?: string;
  mealType?: string;
  estimatedPrepTime?: string;
  // Legacy structure for backward compatibility
  itemsIdentified: string[];
  calories: number;
  macros: {
    protein: string;
    carbs: string;
    fats: string;
  };
  healthRating: number;
  analysis: {
    strengths: string[];
    concerns: string[];
    suitability?: {
      halal: boolean;
      vegan: boolean;
      keto: boolean;
    };
    suggestions: string[];
    macroBalance?: string;
    overallQuality?: string;
  };
}

export interface Recipe {
  recipeTitle: string;
  ingredients: string[];
  steps: string[];
  estimatedCalories: number;
  macros: {
    protein: string;
    carbs: string;
    fats: string;
  };
  tags: string[];
}

export interface DayPlan {
  day: string;
  meals: {
    breakfast: string;
    lunch: string;
    dinner: string;
  };
}

export interface WeekPlan {
  week: DayPlan[];
}

export interface QAResponse {
  answer: string;
}

class ApiService {
  private async makeRequest(endpoint: string, data: any): Promise<any> {
    console.log(`🌐 Attempting Next.js API call to ${endpoint}...`);

    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log(`📡 Next.js API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Next.js API error: ${response.status} - ${errorText}`);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Next.js API call successful');
      return result;
    } catch (error) {
      console.error('❌ Next.js API request failed:', error);
      throw error;
    }
  }

  async scanMeal(imageUri: string): Promise<MealAnalysis> {
    try {
      // First try the Next.js API
      const imageBase64 = await this.convertImageToBase64(imageUri);

      try {
        return await this.makeRequest('/api/gemini', {
          feature: 'scan',
          image: imageBase64,
        });
      } catch (nextjsError) {
        console.log('Next.js API unavailable, trying direct Gemini API');
        // Fallback to direct Gemini API
        return await this.scanMealDirect(imageBase64);
      }
    } catch (error) {
      // Return mock data if all APIs fail (for development)
      console.warn('All APIs failed, using mock data for meal scan:', error);
      return this.getMockMealAnalysis();
    }
  }

  private async convertImageToBase64(imageUri: string): Promise<string> {
    const response = await fetch(imageUri);
    const blob = await response.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]); // Remove data:image/jpeg;base64, prefix
      };
      reader.readAsDataURL(blob);
    });
  }

  private async scanMealDirect(imageBase64: string): Promise<MealAnalysis> {
    console.log('🍽️ Calling Gemini API directly for enhanced meal analysis...');

    const systemPrompt = `You are NutriVision, an expert AI nutritionist with advanced food recognition capabilities. Analyze this food image and provide EXTREMELY DETAILED multi-food detection with precise nutrition data and ingredient breakdown.

CRITICAL REQUIREMENTS:
1. Detect ALL visible foods, ingredients, and components
2. Provide COMPLETE nutritional data (calories, protein, carbs, fat, fiber, sugar, sodium, vitamins)
3. Identify individual ingredients, not just dish names
4. Estimate precise portions and weights
5. Calculate accurate nutritional values based on visible portions

IMPORTANT: Return ONLY a valid JSON object in this exact format:

{
  "mealTitle": "Descriptive meal name",
  "detectedFoods": [
    {
      "name": "Specific ingredient/food name (e.g., 'Grilled Chicken Breast', 'Brown Rice', 'Steamed Broccoli')",
      "portion": "Precise portion with weight (e.g., '4 oz (113g)', '1/2 cup (90g)', '1 medium (150g)')",
      "confidence": 95,
      "calories": 150,
      "protein": 25,
      "carbs": 10,
      "fat": 5,
      "fiber": 2,
      "sugar": 1,
      "sodium": 65,
      "ingredients": ["Main ingredient", "Seasoning", "Cooking oil"],
      "cookingMethod": "grilled/baked/fried/steamed/raw",
      "freshness": "fresh/processed/canned"
    }
  ],
  "totalNutrition": {
    "calories": 450,
    "protein": 35,
    "carbs": 25,
    "fat": 18,
    "fiber": 8,
    "sugar": 12,
    "sodium": 680,
    "calcium": 120,
    "iron": 3.5,
    "vitaminC": 45,
    "vitaminA": 850
  },
  "healthRating": 8,
  "analysis": {
    "strengths": ["High protein content", "Good fiber", "Rich in vitamins"],
    "concerns": ["High sodium", "Processed ingredients"],
    "suggestions": ["Add more vegetables", "Reduce salt", "Choose whole grains"],
    "macroBalance": "High protein, moderate carbs, low fat",
    "overallQuality": "Nutritious with room for improvement"
  },
  "mealType": "lunch",
  "estimatedPrepTime": "15 minutes",
  "detectedIngredients": ["Chicken breast", "Rice", "Broccoli", "Olive oil", "Salt", "Pepper"],
  "estimatedWeight": "350g total"
}

ANALYSIS INSTRUCTIONS:
1. Identify EVERY visible food component (proteins, vegetables, grains, sauces, garnishes)
2. Break down complex dishes into individual ingredients
3. Estimate weights and portions based on visual cues (plate size, utensil comparison)
4. Provide complete nutritional breakdown including micronutrients
5. Assess cooking methods and their impact on nutrition
6. Rate freshness and processing level of ingredients
7. Give specific, actionable health recommendations

Be extremely detailed and accurate. Focus on INGREDIENTS, not just dish names.`;

    const requestBody = {
      contents: [{
        parts: [
          {
            text: systemPrompt
          },
          {
            inline_data: {
              mime_type: "image/jpeg",
              data: imageBase64
            }
          }
        ]
      }],
      generationConfig: {
        temperature: 0.3,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    };

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error response:', errorText);
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const result = await response.json();
      const text = result.candidates[0].content.parts[0].text;

      console.log('📝 Raw meal analysis response:', text.substring(0, 200) + '...');

      try {
        const cleanedText = cleanJsonString(text);
        const analysis = JSON.parse(cleanedText);

        // Validate the enhanced structure
        if (!analysis.detectedFoods || !Array.isArray(analysis.detectedFoods)) {
          throw new Error('Invalid meal analysis structure: missing detectedFoods array');
        }

        console.log('✅ Enhanced meal analysis completed with', analysis.detectedFoods.length, 'foods detected');
        return analysis;
      } catch (parseError) {
        console.error('❌ Failed to parse JSON from Gemini:', text);
        console.error('Parse error:', parseError);
        throw new Error('Received invalid JSON response from AI');
      }
    } catch (error) {
      console.error('❌ Direct Gemini API call failed for meal analysis:', error);
      throw error;
    }
  }

  async generateRecipe(query: string): Promise<Recipe> {
    try {
      // First try the Next.js API
      try {
        return await this.makeRequest('/api/gemini', {
          feature: 'recipe',
          query,
        });
      } catch (nextjsError) {
        console.log('Next.js API unavailable, trying direct Gemini API');
        // Fallback to direct Gemini API
        return await this.generateRecipeDirect(query);
      }
    } catch (error) {
      // Return mock data if all APIs fail (for development)
      console.warn('All APIs failed, using mock data for recipe generation:', error);
      return this.getMockRecipe(query);
    }
  }

  private async generateRecipeDirect(query: string): Promise<Recipe> {
    console.log('🍳 Calling Gemini API directly for recipe generation...');

    const systemPrompt = `You are a creative and health-conscious chef AI. Generate delicious recipes based on the user's query. Always return a single JSON object in this exact format: {"recipeTitle": "...","ingredients": [...],"steps": [...],"estimatedCalories": 0,"macros": { "protein": "...", "carbs": "...", "fats": "..." },"tags": ["healthy", "quick", etc.]}. Make it practical and delicious.`;

    const requestBody = {
      contents: [{
        parts: [{
          text: `${systemPrompt}\n\nGenerate a recipe for: ${query}`
        }]
      }],
      generationConfig: {
        temperature: 0.8,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    };

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error response:', errorText);
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const result = await response.json();
      const text = result.candidates[0].content.parts[0].text;

      try {
        const cleanedText = cleanJsonString(text);
        const recipe = JSON.parse(cleanedText);
        console.log('✅ Recipe generated successfully:', recipe.recipeTitle);
        return recipe;
      } catch (e) {
        console.error('Failed to parse JSON from Gemini:', text);
        throw new Error('Received invalid JSON response from AI');
      }
    } catch (error) {
      console.error('❌ Direct Gemini API call failed for recipe:', error);
      throw error;
    }
  }

  async generateMealPlan(goal: string = 'healthy eating'): Promise<WeekPlan> {
    try {
      // First try the Next.js API
      try {
        return await this.makeRequest('/api/gemini', {
          feature: 'plan',
          goal,
        });
      } catch (nextjsError) {
        console.log('Next.js API unavailable, trying direct Gemini API');
        // Fallback to direct Gemini API
        return await this.generateMealPlanDirect(goal);
      }
    } catch (error) {
      // Return mock data if all APIs fail (for development)
      console.warn('All APIs failed, using mock data for meal plan:', error);
      return this.getMockMealPlan(goal);
    }
  }

  private async generateMealPlanDirect(goal: string): Promise<WeekPlan> {
    console.log('🍽️ Calling Gemini API directly for meal plan generation...');

    const systemPrompt = `You are a professional diet coach. Create a 7-day meal plan based on the user's goal and preferences. Each day must include breakfast, lunch, and dinner. Return ONLY a valid JSON object in this exact format:

{
  "week": [
    {
      "day": "Monday",
      "meals": {
        "breakfast": "Overnight oats with berries",
        "lunch": "Quinoa salad with vegetables",
        "dinner": "Grilled salmon with asparagus"
      }
    },
    {
      "day": "Tuesday",
      "meals": {
        "breakfast": "Avocado toast with eggs",
        "lunch": "Mediterranean wrap",
        "dinner": "Chicken stir fry"
      }
    }
  ]
}

Make sure to include all 7 days (Monday through Sunday). Keep meals practical and aligned with the goal: ${goal}`;

    const requestBody = {
      contents: [{
        parts: [{
          text: systemPrompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    };

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error response:', errorText);
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const result = await response.json();
      const text = result.candidates[0].content.parts[0].text;

      console.log('📝 Raw meal plan response:', text.substring(0, 200) + '...');

      try {
        const cleanedText = cleanJsonString(text);
        const mealPlan = JSON.parse(cleanedText);

        // Validate the structure
        if (!mealPlan.week || !Array.isArray(mealPlan.week)) {
          throw new Error('Invalid meal plan structure: missing week array');
        }

        // Ensure we have 7 days
        if (mealPlan.week.length !== 7) {
          console.warn(`⚠️ Expected 7 days, got ${mealPlan.week.length}`);
        }

        console.log('✅ Meal plan generated successfully with', mealPlan.week.length, 'days');
        return mealPlan;
      } catch (parseError) {
        console.error('❌ Failed to parse JSON from Gemini:', text);
        console.error('Parse error:', parseError);
        throw new Error('Received invalid JSON response from AI');
      }
    } catch (error) {
      console.error('❌ Direct Gemini API call failed for meal plan:', error);
      throw error;
    }
  }

  async askQuestion(question: string): Promise<QAResponse> {
    try {
      // First try the Next.js API
      try {
        return await this.makeRequest('/api/gemini', {
          feature: 'qa',
          question,
        });
      } catch (nextjsError) {
        console.log('Next.js API unavailable, trying direct Gemini API');
        // Fallback to direct Gemini API
        return await this.askQuestionDirect(question);
      }
    } catch (error) {
      // Return mock data if all APIs fail (for development)
      console.warn('All APIs failed, using mock data for Q&A:', error);
      return this.getMockQAResponse(question);
    }
  }

  private async askQuestionDirect(question: string): Promise<QAResponse> {
    console.log('🤖 Calling Gemini API directly for Q&A...');

    const systemPrompt = `You are a world-class AI nutritionist. Give expert but easy-to-understand answers to health and nutrition questions. Be friendly, accurate, and personalized. Always give 2–3 practical suggestions. Avoid giving medical advice. Keep responses concise and helpful.`;

    const requestBody = {
      contents: [{
        parts: [{
          text: `${systemPrompt}\n\nQuestion: ${question}`
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    };

    console.log('📡 Making request to Gemini API...');

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error response:', errorText);
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Gemini API response received');

      if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
        console.error('❌ Invalid response structure:', result);
        throw new Error('Invalid response structure from Gemini API');
      }

      const text = result.candidates[0].content.parts[0].text;
      console.log('✅ Extracted response text:', text.substring(0, 100) + '...');

      return { answer: text };
    } catch (error) {
      console.error('❌ Direct Gemini API call failed:', error);
      throw error;
    }
  }

  // Mock data methods for development/fallback
  private getMockMealAnalysis(): MealAnalysis {
    return {
      mealTitle: "Grilled Chicken Salad",
      detectedFoods: [
        {
          name: "Grilled Chicken Breast",
          portion: "4 oz (113g)",
          confidence: 95,
          calories: 185,
          protein: 35,
          carbs: 0,
          fat: 4,
          fiber: 0,
          sugar: 0,
          sodium: 74,
          ingredients: ["Chicken breast", "Olive oil", "Salt", "Pepper"],
          cookingMethod: "grilled",
          freshness: "fresh"
        },
        {
          name: "Mixed Greens",
          portion: "2 cups (60g)",
          confidence: 90,
          calories: 20,
          protein: 2,
          carbs: 4,
          fat: 0,
          fiber: 2,
          sugar: 2,
          sodium: 22,
          ingredients: ["Lettuce", "Spinach", "Arugula"],
          cookingMethod: "raw",
          freshness: "fresh"
        },
        {
          name: "Cherry Tomatoes",
          portion: "1/2 cup (75g)",
          confidence: 88,
          calories: 15,
          protein: 1,
          carbs: 3,
          fat: 0,
          fiber: 1,
          sugar: 2,
          sodium: 3,
          ingredients: ["Cherry tomatoes"],
          cookingMethod: "raw",
          freshness: "fresh"
        }
      ],
      totalNutrition: {
        calories: 320,
        protein: 28,
        carbs: 12,
        fat: 18,
        fiber: 8,
        sugar: 6,
        sodium: 450,
        calcium: 120,
        iron: 3.5,
        vitaminC: 45,
        vitaminA: 850
      },
      healthRating: 8,
      analysis: {
        strengths: [
          "High protein content supports muscle building",
          "Rich in vitamins from fresh vegetables",
          "Low in processed ingredients"
        ],
        concerns: [
          "Could benefit from more colorful vegetables",
          "Consider adding healthy fats like avocado"
        ],
        suggestions: [
          "Add more colorful vegetables for antioxidants",
          "Consider adding nuts or seeds for healthy fats",
          "Include a complex carb like quinoa for sustained energy"
        ],
        macroBalance: "High protein, low carbs, moderate fat",
        overallQuality: "Excellent nutritious meal"
      },
      mealType: "lunch",
      estimatedPrepTime: "15 minutes",
      detectedIngredients: ["Chicken breast", "Mixed greens", "Cherry tomatoes", "Olive oil", "Salt", "Pepper"],
      estimatedWeight: "250g total",
      // Legacy fields for backward compatibility
      itemsIdentified: ["Grilled chicken breast", "Mixed greens", "Cherry tomatoes", "Cucumber", "Olive oil dressing"],
      calories: 320,
      macros: {
        protein: "28g",
        carbs: "12g",
        fats: "18g",
      },
    };
  }

  private getMockRecipe(query: string): Recipe {
    return {
      recipeTitle: `Healthy ${query} Recipe`,
      ingredients: [
        "2 cups mixed vegetables",
        "1 cup protein source",
        "2 tbsp olive oil",
        "1 tsp herbs and spices",
        "Salt and pepper to taste"
      ],
      steps: [
        "Prepare all ingredients by washing and chopping",
        "Heat olive oil in a large pan over medium heat",
        "Add protein and cook until golden brown",
        "Add vegetables and cook until tender",
        "Season with herbs, salt, and pepper",
        "Serve hot and enjoy!"
      ],
      estimatedCalories: 350,
      macros: {
        protein: "25g",
        carbs: "20g",
        fats: "15g",
      },
      tags: ["healthy", "balanced", "quick"],
    };
  }

  private getMockMealPlan(goal: string): WeekPlan {
    const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    
    return {
      week: days.map(day => ({
        day,
        meals: {
          breakfast: `Healthy breakfast for ${goal.toLowerCase()}`,
          lunch: `Nutritious lunch supporting ${goal.toLowerCase()}`,
          dinner: `Balanced dinner aligned with ${goal.toLowerCase()}`,
        },
      })),
    };
  }

  // Public method for Ask AI functionality
  async askNutritionQuestion(question: string): Promise<string> {
    console.log('🤖 Calling Gemini API directly for nutrition question...');

    try {
      // Try direct Gemini API first
      const response = await this.askNutritionQuestionDirect(question);
      return response;
    } catch (error) {
      console.error('❌ Direct Gemini API call failed for nutrition question:', error);

      // Fallback to mock response
      console.log('🔄 Using fallback mock response for nutrition question');
      const mockResponse = this.getMockQAResponse(question);
      return mockResponse.answer;
    }
  }

  private async askNutritionQuestionDirect(question: string): Promise<string> {
    const systemPrompt = `You are NutriAI, an expert nutrition assistant. You provide helpful, accurate, and personalized nutrition advice.

User Profile: ${JSON.stringify(userProfile)}

Guidelines:
- Provide evidence-based nutrition advice
- Be conversational and supportive
- Consider the user's profile when giving recommendations
- If asked about medical conditions, recommend consulting healthcare professionals
- Keep responses concise but informative (2-3 paragraphs max)
- Use emojis sparingly for a friendly tone

Question: ${question}

Provide a helpful, personalized response:`;

    const requestBody = {
      contents: [{
        parts: [{
          text: systemPrompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    };

    try {
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error response:', errorText);
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const result = await response.json();
      const answer = result.candidates[0].content.parts[0].text;

      console.log('✅ Nutrition question answered successfully');
      return answer.trim();
    } catch (error) {
      console.error('❌ Direct Gemini API call failed for nutrition question:', error);
      throw error;
    }
  }

  private getMockQAResponse(question: string): QAResponse {
    return {
      answer: `Thank you for your question about "${question}". Based on current nutrition science and your profile (${userProfile.name}), I'd recommend focusing on balanced nutrition with plenty of whole foods. Here are some key points to consider: 1) Maintain a balanced intake of macronutrients, 2) Stay hydrated throughout the day, 3) Include a variety of colorful fruits and vegetables. Would you like me to elaborate on any specific aspect of nutrition?`,
    };
  }
}

export default new ApiService();
