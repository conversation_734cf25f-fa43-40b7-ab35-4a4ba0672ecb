import {
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  withRepeat,
  interpolate,
  Easing,
  runOnJS,
  SharedValue,
} from 'react-native-reanimated';
import { Haptics } from 'expo-haptics';

// Animation Configuration
export const AnimationConfig = {
  // Spring Physics
  spring: {
    gentle: { damping: 20, stiffness: 200 },
    bouncy: { damping: 15, stiffness: 400 },
    snappy: { damping: 25, stiffness: 500 },
    wobbly: { damping: 10, stiffness: 300 },
  },
  
  // Timing Curves
  timing: {
    fast: { duration: 200, easing: Easing.out(Easing.cubic) },
    medium: { duration: 400, easing: Easing.out(Easing.cubic) },
    slow: { duration: 600, easing: Easing.out(Easing.cubic) },
    elastic: { duration: 800, easing: Easing.elastic(1.2) },
  },
  
  // Stagger Delays
  stagger: {
    fast: 50,
    medium: 100,
    slow: 150,
  },
};

// Enhanced Animation Presets
export const AnimationPresets = {
  // Scale Animations
  scaleIn: (delay = 0) => withDelay(
    delay,
    withSpring(1, AnimationConfig.spring.bouncy)
  ),
  
  scaleOut: (delay = 0) => withDelay(
    delay,
    withSpring(0, AnimationConfig.spring.snappy)
  ),
  
  // Bounce Animation
  bounce: (scale: SharedValue, callback?: () => void) => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withSpring(1.05, AnimationConfig.spring.bouncy),
      withSpring(1, AnimationConfig.spring.gentle),
      callback ? withTiming(1, { duration: 0 }, () => runOnJS(callback)()) : withTiming(1, { duration: 0 })
    );
  },
  
  // Pulse Animation
  pulse: (scale: SharedValue, intensity = 1.1) => {
    scale.value = withRepeat(
      withSequence(
        withTiming(intensity, AnimationConfig.timing.medium),
        withTiming(1, AnimationConfig.timing.medium)
      ),
      -1,
      false
    );
  },
  
  // Shake Animation
  shake: (translateX: SharedValue, intensity = 10) => {
    translateX.value = withSequence(
      withTiming(-intensity, { duration: 50 }),
      withTiming(intensity, { duration: 50 }),
      withTiming(-intensity, { duration: 50 }),
      withTiming(intensity, { duration: 50 }),
      withTiming(0, { duration: 50 })
    );
  },
  
  // Fade Animations
  fadeIn: (opacity: SharedValue, delay = 0) => {
    opacity.value = withDelay(delay, withTiming(1, AnimationConfig.timing.medium));
  },
  
  fadeOut: (opacity: SharedValue, delay = 0) => {
    opacity.value = withDelay(delay, withTiming(0, AnimationConfig.timing.medium));
  },
  
  // Slide Animations
  slideInFromLeft: (translateX: SharedValue, delay = 0) => {
    translateX.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
  },
  
  slideInFromRight: (translateX: SharedValue, delay = 0) => {
    translateX.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
  },
  
  slideInFromTop: (translateY: SharedValue, delay = 0) => {
    translateY.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
  },
  
  slideInFromBottom: (translateY: SharedValue, delay = 0) => {
    translateY.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
  },
  
  // Rotation Animations
  rotate360: (rotation: SharedValue, duration = 1000) => {
    rotation.value = withTiming(360, { duration }, () => {
      rotation.value = 0;
    });
  },
  
  // Complex Animations
  morphIn: (scale: SharedValue, opacity: SharedValue, delay = 0) => {
    scale.value = withDelay(delay, withSpring(1, AnimationConfig.spring.bouncy));
    opacity.value = withDelay(delay, withTiming(1, AnimationConfig.timing.medium));
  },
  
  morphOut: (scale: SharedValue, opacity: SharedValue, delay = 0) => {
    scale.value = withDelay(delay, withSpring(0.8, AnimationConfig.spring.snappy));
    opacity.value = withDelay(delay, withTiming(0, AnimationConfig.timing.fast));
  },
  
  // Staggered List Animation
  staggeredListIn: (items: SharedValue[], delay = 0, stagger = AnimationConfig.stagger.medium) => {
    items.forEach((item, index) => {
      item.value = withDelay(
        delay + index * stagger,
        withSpring(1, AnimationConfig.spring.bouncy)
      );
    });
  },
  
  // Card Flip Animation
  cardFlip: (rotateY: SharedValue, callback?: () => void) => {
    rotateY.value = withSequence(
      withTiming(90, AnimationConfig.timing.fast),
      withTiming(0, AnimationConfig.timing.fast, callback ? () => runOnJS(callback)() : undefined)
    );
  },
  
  // Elastic Scale
  elasticScale: (scale: SharedValue, targetScale = 1.2) => {
    scale.value = withSequence(
      withSpring(targetScale, AnimationConfig.spring.wobbly),
      withSpring(1, AnimationConfig.spring.gentle)
    );
  },
  
  // Progress Animation
  progressFill: (progress: SharedValue, targetProgress: number, duration = 1000) => {
    progress.value = withTiming(targetProgress, { duration, easing: Easing.out(Easing.cubic) });
  },
  
  // Breathing Animation
  breathe: (scale: SharedValue, intensity = 0.05) => {
    scale.value = withRepeat(
      withSequence(
        withTiming(1 + intensity, { duration: 2000, easing: Easing.inOut(Easing.sine) }),
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.sine) })
      ),
      -1,
      false
    );
  },
};

// Gesture Animation Helpers
export const GestureAnimations = {
  // Press Animation
  pressIn: (scale: SharedValue, opacity: SharedValue) => {
    scale.value = withSpring(0.95, AnimationConfig.spring.snappy);
    opacity.value = withTiming(0.8, AnimationConfig.timing.fast);
  },
  
  pressOut: (scale: SharedValue, opacity: SharedValue) => {
    scale.value = withSpring(1, AnimationConfig.spring.bouncy);
    opacity.value = withTiming(1, AnimationConfig.timing.fast);
  },
  
  // Long Press Animation
  longPressStart: (scale: SharedValue) => {
    scale.value = withSpring(1.05, AnimationConfig.spring.gentle);
  },
  
  longPressEnd: (scale: SharedValue) => {
    scale.value = withSpring(1, AnimationConfig.spring.bouncy);
  },
  
  // Swipe Feedback
  swipeStart: (translateX: SharedValue, direction: 'left' | 'right') => {
    const target = direction === 'left' ? -10 : 10;
    translateX.value = withSpring(target, AnimationConfig.spring.snappy);
  },
  
  swipeEnd: (translateX: SharedValue) => {
    translateX.value = withSpring(0, AnimationConfig.spring.bouncy);
  },
};

// Haptic Feedback Integration
export const HapticAnimations = {
  // Light Impact with Scale
  lightImpact: (scale: SharedValue) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    AnimationPresets.bounce(scale);
  },
  
  // Medium Impact with Pulse
  mediumImpact: (scale: SharedValue) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    scale.value = withSequence(
      withTiming(1.1, { duration: 100 }),
      withSpring(1, AnimationConfig.spring.bouncy)
    );
  },
  
  // Heavy Impact with Shake
  heavyImpact: (translateX: SharedValue) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    AnimationPresets.shake(translateX, 5);
  },
  
  // Success Feedback
  success: (scale: SharedValue, callback?: () => void) => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    AnimationPresets.elasticScale(scale, 1.3);
    if (callback) {
      setTimeout(callback, 500);
    }
  },
  
  // Error Feedback
  error: (translateX: SharedValue, callback?: () => void) => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    AnimationPresets.shake(translateX, 15);
    if (callback) {
      setTimeout(callback, 300);
    }
  },
  
  // Warning Feedback
  warning: (scale: SharedValue) => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    scale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1.1, { duration: 100 }),
      withSpring(1, AnimationConfig.spring.bouncy)
    );
  },
};

// Interpolation Helpers
export const InterpolationHelpers = {
  // Scale interpolation
  scaleInterpolate: (value: SharedValue, inputRange: number[], outputRange: number[]) => {
    return interpolate(value.value, inputRange, outputRange, 'clamp');
  },
  
  // Opacity interpolation
  opacityInterpolate: (value: SharedValue, inputRange: number[], outputRange: number[]) => {
    return interpolate(value.value, inputRange, outputRange, 'clamp');
  },
  
  // Color interpolation helper
  colorInterpolate: (progress: number, colors: string[]) => {
    // This would need a color interpolation library
    // For now, return the first color
    return colors[0];
  },
  
  // Rotation interpolation
  rotationInterpolate: (value: SharedValue, inputRange: number[], outputRange: number[]) => {
    return `${interpolate(value.value, inputRange, outputRange, 'clamp')}deg`;
  },
};

// Animation Sequences
export const AnimationSequences = {
  // Loading sequence
  loading: (elements: SharedValue[]) => {
    elements.forEach((element, index) => {
      element.value = withDelay(
        index * 200,
        withRepeat(
          withSequence(
            withTiming(0.5, { duration: 600 }),
            withTiming(1, { duration: 600 })
          ),
          -1,
          false
        )
      );
    });
  },
  
  // Success sequence
  successSequence: (elements: SharedValue[], callback?: () => void) => {
    elements.forEach((element, index) => {
      element.value = withDelay(
        index * 100,
        withSequence(
          withSpring(1.2, AnimationConfig.spring.bouncy),
          withSpring(1, AnimationConfig.spring.gentle)
        )
      );
    });
    
    if (callback) {
      setTimeout(callback, elements.length * 100 + 500);
    }
  },
  
  // Reveal sequence
  revealSequence: (elements: SharedValue[]) => {
    elements.forEach((element, index) => {
      element.value = withDelay(
        index * 150,
        withSpring(1, AnimationConfig.spring.bouncy)
      );
    });
  },
};

export default {
  AnimationConfig,
  AnimationPresets,
  GestureAnimations,
  HapticAnimations,
  InterpolationHelpers,
  AnimationSequences,
};
